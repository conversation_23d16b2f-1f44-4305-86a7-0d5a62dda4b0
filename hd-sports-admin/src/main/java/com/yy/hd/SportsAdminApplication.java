package com.yy.hd;

import com.yy.hd.s2s.S2sNameAutoDetect;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * <AUTHOR>
 */
@EnableDubbo
@SpringBootApplication
public class SportsAdminApplication {

    public static void main(String[] args) {
        // 自动检测s2sname
        S2sNameAutoDetect.init();
        SpringApplication.run(SportsAdminApplication.class, args);
    }
}

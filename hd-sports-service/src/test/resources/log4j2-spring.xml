<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="INFO" monitorInterval="180">
    <Properties>
        <Property name="pattern">[%level] %d{HH:mm:ss} [%X{trace_id}] [%thread] [%file:%line] - %M %msg%n</Property>
    </Properties>

    <Appenders>
        <Console name="Console" target="SYSTEM_OUT" follow="true">
            <ThresholdFilter level="INFO" onMatch="ACCEPT"/>
            <PatternLayout pattern="${pattern}" charset="UTF-8"/>
        </Console>
    </Appenders>

    <Loggers>
        <Logger name="org.apache.catalina.startup.DigesterFactory" level="error"/>
        <Logger name="org.apache.catalina.util.LifecycleBase" level="error"/>
        <Logger name="org.apache.coyote.http11.Http11NioProtocol" level="warn"/>
        <Logger name="org.apache.sshd.common.util.SecurityUtils" level="warn"/>
        <Logger name="org.apache.tomcat.util.net.NioSelectorPool" level="warn"/>
        <Logger name="org.eclipse.jetty.util.component.AbstractLifeCycle" level="error"/>
        <Logger name="org.hibernate.validator.internal.util.Version" level="warn"/>
        <Logger name="org.springframework.boot.actuate.endpoint.jmx" level="warn"/>
        <Logger name="com.ctrip.framework.apollo.spring.annotation.SpringValueProcessor" level="warn"/>

        <logger name="com.yy.ent.clients.s2s" level="ERROR" includeLocation="true" additivity="false">
            <AppenderRef ref="Console"/>
        </logger>
        <logger name="com.yy.ent.client.s2s" level="ERROR" includeLocation="true" additivity="false">
            <AppenderRef ref="Console"/>
        </logger>

        <Logger name="com.ctrip.framework.apollo.spring.annotation.SpringValueProcessor" level="warn"/>
        <Logger name="org.apache.dubbo.common.annotation.YrpcFunctionNameMapping" level="warn"/>
        <Logger name="org.apache.dubbo.registry.s2s.S2sRegistry" level="error"/>
        <Logger name="com.alibaba.spring.util.BeanRegistrar" level="warn"/>
        <Logger name="org.apache.dubbo.config.spring.beans.factory.annotation.ServiceAnnotationBeanPostProcessor"
                level="warn"/>
        <Logger name="org.springframework.context.annotation.ConfigurationClassPostProcessor" level="warn"/>
        <Logger name="org.apache.dubbo.config.spring.extension.SpringExtensionFactory" level="warn"/>

        <Root level="INFO" includeLocation="true">
            <AppenderRef ref="Console"/>
        </Root>
    </Loggers>
</Configuration>

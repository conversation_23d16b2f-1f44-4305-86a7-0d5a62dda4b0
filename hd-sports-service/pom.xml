<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yy.hd</groupId>
        <artifactId>hd-sports</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>hd-sports-service</artifactId>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>${project.parent.groupId}</groupId>
            <artifactId>hd-sports-persist</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <dependency>
            <groupId>${project.parent.groupId}</groupId>
            <artifactId>hd-sports-protocol</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.2.5</version>
                <configuration>
                    <skipTests>true</skipTests>
                    <argLine>
                        -ea
                        --add-opens java.base/java.lang=ALL-UNNAMED
                        --add-opens java.base/sun.reflect.generics.reflectiveObjects=ALL-UNNAMED
                        --add-opens java.base/java.util=ALL-UNNAMED
                        --add-opens java.base/java.util.concurrent=ALL-UNNAMED
                        --add-opens java.base/sun.net.util=ALL-UNNAMED
                        -Dspring.profiles.active=dev
                        -Ddubbo.service.shutdown.wait=1
                        -DMY_HOST_IP=***********
                    </argLine>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
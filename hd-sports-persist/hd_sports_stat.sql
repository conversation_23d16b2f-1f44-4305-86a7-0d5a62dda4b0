-- 数据统计相关表

-- 关键信息
CREATE TABLE IF NOT EXISTS `hd_sports`.`stat_key_info`
(
    `id`                   bigint(20) AUTO_INCREMENT COMMENT '自增唯一ID',
    `date`                 date        NOT NULL COMMENT '统计日期',
    `arena_id`             int(11)     NOT NULL COMMENT '玩法类型:超能运动会/All',
    `platform`             varchar(16) NOT NULL COMMENT '渠道类型:All/pc端/Yo交友/yo语音, web端, 好看端, 贴吧端',
    `frag_count`           int(11)     NOT NULL COMMENT '碎片流水总额',
    `gift_spending`        int(11)     NOT NULL COMMENT '礼物支出:紫水晶/金钻',
    `total_spending`       int(11)     NOT NULL COMMENT '总支出:礼物支出+装扮碎片流水',
    `play_count`           int(11)     NOT NULL COMMENT '参与/付费人数',
    `total_win_user`       int(11) COMMENT '成功总人数',
    `win_count`            int(11) COMMENT '累计成功人数(投入>奖励)',
    `lose_count`           int(11) COMMENT '累计失败人数(投入<奖励)',
    `total_props_value`    int(20)     NOT NULL COMMENT '剩余累计道具数额汇总(历史总额)',
    `sum_fruit`            int(11)     NOT NULL COMMENT '模拟收入(果实消耗流水)',
    `spending_user_ratio`  varchar(16) COMMENT '人均支出=果实消耗流水/参与人数*100',
    `spending_fruit_ratio` varchar(16) COMMENT '发放占比=总支出/模拟收入*100',
    `gift_fruit_ratio`     varchar(16) COMMENT '粗发放占比=礼物支出/模拟收入*100',
    `fruit_gift_ratio`     varchar(16) COMMENT '粗偏移=模拟收入/礼物支出*100',
    `fruit_spending_ratio` varchar(16) COMMENT '总偏移=模拟收入/总支出*100',
    `create_time`          datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`          datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后一次助力值更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_date` (`date`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4 COMMENT '超能运动会关键信息统计';

-- 参与用户区间分布
CREATE TABLE IF NOT EXISTS `hd_sports`.`stat_paid_range_info`
(
    `id`              bigint(20) AUTO_INCREMENT COMMENT '自增唯一ID',
    `date`            date        NOT NULL COMMENT '统计日期',
    `arena_id`        int(11)     NOT NULL COMMENT '玩法类型:超能运动会/All',
    `platform`        varchar(16) NOT NULL COMMENT '渠道类型:All/pc端/Yo交友/yo语音, web端, 好看端, 贴吧端',
    `range_id`        int(11)     NOT NULL COMMENT '参与流水区间 流水(元)1000元一个区间',
    `sum_income`      int(11)     NOT NULL COMMENT '参与流水(元)',
    `win_income`      int(11)     NOT NULL COMMENT '成功流水(元)',
    `spending_ratio`  varchar(16) NOT NULL COMMENT '发放占比:成功流水(元)/参与流水(元)*100',
    `play_user`       int(11)     NOT NULL COMMENT '参与用户数',
    `win_user`        int(11)     NOT NULL COMMENT '成功用户数',
    `total_win_user`  int(11)     NOT NULL COMMENT '累计成功用户数',
    `total_loss_user` int(11)     NOT NULL COMMENT '累计失败用户数',
    `create_time`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后一次助力值更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_date` (`date`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4 COMMENT '超能运动会参与用户区间分布统计';

-- 分时段监控
CREATE TABLE IF NOT EXISTS `hd_sports`.`stat_period_range_info`
(
    `id`              bigint(20) AUTO_INCREMENT COMMENT '自增唯一ID',
    `date`            date        NOT NULL COMMENT '统计日期',
    `arena_id`        int(11)     NOT NULL COMMENT '玩法类型:超能运动会/All',
    `platform`        varchar(16) NOT NULL COMMENT '渠道类型:All/pc端/Yo交友/yo语音, web端, 好看端, 贴吧端',
    `range_id`        int(11)     NOT NULL COMMENT '参与玩法时段 一个小时一个区间',
    `sum_income`      int(11)     NOT NULL COMMENT '参与流水(元)',
    `win_income`      int(11)     NOT NULL COMMENT '成功流水(元)',
    `spending_ratio`  varchar(16) NOT NULL COMMENT '发放占比:成功流水(元)/参与流水(元)*100',
    `play_user`       int(11)     NOT NULL COMMENT '参与用户数',
    `win_user`        int(11)     NOT NULL COMMENT '成功用户数',
    `total_win_user`  int(11)     NOT NULL COMMENT '累计成功用户数',
    `total_loss_user` int(11)     NOT NULL COMMENT '累计失败用户数',
    `create_time`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后一次助力值更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_date` (`date`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4 COMMENT '超能运动会参与用户区间分布统计';

-- 参与位置分布
CREATE TABLE IF NOT EXISTS `hd_sports`.`stat_position_info`
(
    `id`           bigint(20) AUTO_INCREMENT COMMENT '自增唯一ID',
    `date`         date        NOT NULL COMMENT '统计日期',
    `position`     int(11)     NOT NULL COMMENT '参与位置',
    `amount`       int(11)     NOT NULL COMMENT '参与流水',
    `award_amount` int(11)     NOT NULL COMMENT '发放流水',
    `award_ratio`  varchar(16) NOT NULL COMMENT '发放占比=发放流水/参与流水*100%',
    `create_time`  datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后一次助力值更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_date` (`date`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4 COMMENT '超能运动会参与位置分布统计';

-- 用户留存
CREATE TABLE IF NOT EXISTS `hd_sports`.`stat_retention_info`
(
    `id`                  bigint(20) AUTO_INCREMENT COMMENT '自增唯一ID',
    `date`                date        NOT NULL COMMENT '统计日期',
    `platform`            varchar(16) NOT NULL COMMENT '渠道类型:All/pc端/Yo交友/yo语音, web端, 好看端, 贴吧端',
    `user_type`           varchar(32) NOT NULL COMMENT '用户类型 ALL、累计成功、累计失败,新用户,老用户',
    `user_count`          int(11)     NOT NULL COMMENT '用户数',
    `retention_1`         varchar(32) NOT NULL COMMENT '次日留存',
    `retention_7`         varchar(32) NOT NULL COMMENT '7日留存',
    `retention_30`        varchar(32) NOT NULL COMMENT '30日留存',
    `income_retention_1`  varchar(32) NOT NULL COMMENT '次日参与流水留存',
    `income_retention_7`  varchar(32) NOT NULL COMMENT '7日参与流水留存',
    `income_retention_30` varchar(32) NOT NULL COMMENT '30日参与流水留存',
    `create_time`         datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`         datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后一次助力值更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_date` (`date`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4 COMMENT '超能运动会用户留存统计';

-- Top10用户
CREATE TABLE IF NOT EXISTS `hd_sports`.`stat_top_user_info`
(
    `id`                     bigint(20) AUTO_INCREMENT COMMENT '自增唯一ID',
    `date`                   date        NOT NULL COMMENT '统计日期',
    `uid`                    bigint(20)  NOT NULL COMMENT 'uid',
    `seal_amount`            bigint(20)  NOT NULL COMMENT '盖章流水',
    `play_amount`            bigint(20)  NOT NULL COMMENT '参与流水',
    `award_amount`           bigint(20)  NOT NULL COMMENT '发放流水',
    `play_count`             bigint(20)  NOT NULL COMMENT '参与次数',
    `win_count`              bigint(20)  NOT NULL COMMENT '成功次数',
    `prefer_position`        varchar(64) NOT NULL COMMENT '偏好位置',
    `prefer_position_count`  bigint(20)  NOT NULL COMMENT '偏好位置参与次数',
    `prefer_position_amount` bigint(20)  NOT NULL COMMENT '偏好流水总和',
    `play_ratio`             varchar(12) NOT NULL COMMENT '参与次数比=偏好位置参与次数/参与次数*100',
    `amount_ratio`           varchar(12) NOT NULL COMMENT '流水占比=偏好流水总和/参与流水*100',
    `top_sid`                varchar(64) NOT NULL COMMENT '参与流水最高频道',
    `top_sid_amount`         bigint(20)  NOT NULL COMMENT '最高流水频道总和',
    `top_sid_ratio`          varchar(12) NOT NULL COMMENT '流水占比(频道)=最高流水频道总和/参与流水*100',
    `top_ssid`               varchar(64) NOT NULL COMMENT '参与流水最高子频道',
    `top_ssid_amount`        bigint(20)  NOT NULL COMMENT '最高流水子频道总和',
    `top_ssid_ratio`         varchar(12) NOT NULL COMMENT '流水占比(子频道)=最高流水子频道总和/参与流水*100',
    `create_time`            datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`            datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后一次助力值更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_date` (`date`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4 COMMENT '超能运动会Top10用户统计';

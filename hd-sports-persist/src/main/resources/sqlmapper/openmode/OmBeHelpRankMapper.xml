<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.hd.persist.openmode.mapper.OmBeHelpRankMapper">
  <resultMap id="BaseResultMap" type="com.yy.hd.persist.openmode.model.OmBeHelpRank">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dt" jdbcType="INTEGER" property="dt" />
    <result column="uid" jdbcType="BIGINT" property="uid" />
    <result column="help_value" jdbcType="BIGINT" property="helpValue" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, dt, uid, help_value, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from om_be_help_rank
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from om_be_help_rank
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.OmBeHelpRank" useGeneratedKeys="true">
    insert into om_be_help_rank (dt, uid, help_value, 
      create_time, update_time)
    values (#{dt,jdbcType=INTEGER}, #{uid,jdbcType=BIGINT}, #{helpValue,jdbcType=BIGINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.OmBeHelpRank" useGeneratedKeys="true">
    insert into om_be_help_rank
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dt != null">
        dt,
      </if>
      <if test="uid != null">
        uid,
      </if>
      <if test="helpValue != null">
        help_value,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dt != null">
        #{dt,jdbcType=INTEGER},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=BIGINT},
      </if>
      <if test="helpValue != null">
        #{helpValue,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.hd.persist.openmode.model.OmBeHelpRank">
    update om_be_help_rank
    <set>
      <if test="dt != null">
        dt = #{dt,jdbcType=INTEGER},
      </if>
      <if test="uid != null">
        uid = #{uid,jdbcType=BIGINT},
      </if>
      <if test="helpValue != null">
        help_value = #{helpValue,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.hd.persist.openmode.model.OmBeHelpRank">
    update om_be_help_rank
    set dt = #{dt,jdbcType=INTEGER},
      uid = #{uid,jdbcType=BIGINT},
      help_value = #{helpValue,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.hd.persist.openmode.mapper.OmBuyCurrencyRecordMapper">
  <resultMap id="BaseResultMap" type="com.yy.hd.persist.openmode.model.OmBuyCurrencyRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="seq_id" jdbcType="CHAR" property="seqId" />
    <result column="uid" jdbcType="BIGINT" property="uid" />
    <result column="biz_id" jdbcType="INTEGER" property="bizId" />
    <result column="host_name" jdbcType="CHAR" property="hostName" />
    <result column="used_channel" jdbcType="INTEGER" property="usedChannel" />
    <result column="sid" jdbcType="BIGINT" property="sid" />
    <result column="ssid" jdbcType="BIGINT" property="ssid" />
    <result column="be_help_uid" jdbcType="BIGINT" property="beHelpUid" />
    <result column="help_count" jdbcType="BIGINT" property="helpCount" />
    <result column="help_amount" jdbcType="BIGINT" property="helpAmount" />
    <result column="fruit_count" jdbcType="BIGINT" property="fruitCount" />
    <result column="help_value" jdbcType="BIGINT" property="helpValue" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, seq_id, uid, biz_id, host_name, used_channel, sid, ssid, be_help_uid, help_count, 
    help_amount, fruit_count, help_value, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from om_buy_currency_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from om_buy_currency_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.OmBuyCurrencyRecord" useGeneratedKeys="true">
    insert into om_buy_currency_record (seq_id, uid, biz_id, 
      host_name, used_channel, sid, 
      ssid, be_help_uid, help_count, 
      help_amount, fruit_count, help_value, 
      create_time)
    values (#{seqId,jdbcType=CHAR}, #{uid,jdbcType=BIGINT}, #{bizId,jdbcType=INTEGER}, 
      #{hostName,jdbcType=CHAR}, #{usedChannel,jdbcType=INTEGER}, #{sid,jdbcType=BIGINT}, 
      #{ssid,jdbcType=BIGINT}, #{beHelpUid,jdbcType=BIGINT}, #{helpCount,jdbcType=BIGINT}, 
      #{helpAmount,jdbcType=BIGINT}, #{fruitCount,jdbcType=BIGINT}, #{helpValue,jdbcType=BIGINT}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.OmBuyCurrencyRecord" useGeneratedKeys="true">
    insert into om_buy_currency_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="seqId != null">
        seq_id,
      </if>
      <if test="uid != null">
        uid,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="hostName != null">
        host_name,
      </if>
      <if test="usedChannel != null">
        used_channel,
      </if>
      <if test="sid != null">
        sid,
      </if>
      <if test="ssid != null">
        ssid,
      </if>
      <if test="beHelpUid != null">
        be_help_uid,
      </if>
      <if test="helpCount != null">
        help_count,
      </if>
      <if test="helpAmount != null">
        help_amount,
      </if>
      <if test="fruitCount != null">
        fruit_count,
      </if>
      <if test="helpValue != null">
        help_value,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="seqId != null">
        #{seqId,jdbcType=CHAR},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=INTEGER},
      </if>
      <if test="hostName != null">
        #{hostName,jdbcType=CHAR},
      </if>
      <if test="usedChannel != null">
        #{usedChannel,jdbcType=INTEGER},
      </if>
      <if test="sid != null">
        #{sid,jdbcType=BIGINT},
      </if>
      <if test="ssid != null">
        #{ssid,jdbcType=BIGINT},
      </if>
      <if test="beHelpUid != null">
        #{beHelpUid,jdbcType=BIGINT},
      </if>
      <if test="helpCount != null">
        #{helpCount,jdbcType=BIGINT},
      </if>
      <if test="helpAmount != null">
        #{helpAmount,jdbcType=BIGINT},
      </if>
      <if test="fruitCount != null">
        #{fruitCount,jdbcType=BIGINT},
      </if>
      <if test="helpValue != null">
        #{helpValue,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.hd.persist.openmode.model.OmBuyCurrencyRecord">
    update om_buy_currency_record
    <set>
      <if test="seqId != null">
        seq_id = #{seqId,jdbcType=CHAR},
      </if>
      <if test="uid != null">
        uid = #{uid,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=INTEGER},
      </if>
      <if test="hostName != null">
        host_name = #{hostName,jdbcType=CHAR},
      </if>
      <if test="usedChannel != null">
        used_channel = #{usedChannel,jdbcType=INTEGER},
      </if>
      <if test="sid != null">
        sid = #{sid,jdbcType=BIGINT},
      </if>
      <if test="ssid != null">
        ssid = #{ssid,jdbcType=BIGINT},
      </if>
      <if test="beHelpUid != null">
        be_help_uid = #{beHelpUid,jdbcType=BIGINT},
      </if>
      <if test="helpCount != null">
        help_count = #{helpCount,jdbcType=BIGINT},
      </if>
      <if test="helpAmount != null">
        help_amount = #{helpAmount,jdbcType=BIGINT},
      </if>
      <if test="fruitCount != null">
        fruit_count = #{fruitCount,jdbcType=BIGINT},
      </if>
      <if test="helpValue != null">
        help_value = #{helpValue,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.hd.persist.openmode.model.OmBuyCurrencyRecord">
    update om_buy_currency_record
    set seq_id = #{seqId,jdbcType=CHAR},
      uid = #{uid,jdbcType=BIGINT},
      biz_id = #{bizId,jdbcType=INTEGER},
      host_name = #{hostName,jdbcType=CHAR},
      used_channel = #{usedChannel,jdbcType=INTEGER},
      sid = #{sid,jdbcType=BIGINT},
      ssid = #{ssid,jdbcType=BIGINT},
      be_help_uid = #{beHelpUid,jdbcType=BIGINT},
      help_count = #{helpCount,jdbcType=BIGINT},
      help_amount = #{helpAmount,jdbcType=BIGINT},
      fruit_count = #{fruitCount,jdbcType=BIGINT},
      help_value = #{helpValue,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
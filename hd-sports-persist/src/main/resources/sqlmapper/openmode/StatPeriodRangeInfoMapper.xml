<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.hd.persist.openmode.mapper.StatPeriodRangeInfoMapper">
  <resultMap id="BaseResultMap" type="com.yy.hd.persist.openmode.model.StatPeriodRangeInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="arena_id" jdbcType="INTEGER" property="arenaId" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="range_id" jdbcType="INTEGER" property="rangeId" />
    <result column="sum_income" jdbcType="INTEGER" property="sumIncome" />
    <result column="win_income" jdbcType="INTEGER" property="winIncome" />
    <result column="spending_ratio" jdbcType="VARCHAR" property="spendingRatio" />
    <result column="play_user" jdbcType="INTEGER" property="playUser" />
    <result column="win_user" jdbcType="INTEGER" property="winUser" />
    <result column="total_win_user" jdbcType="INTEGER" property="totalWinUser" />
    <result column="total_loss_user" jdbcType="INTEGER" property="totalLossUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, date, arena_id, platform, range_id, sum_income, win_income, spending_ratio, play_user, 
    win_user, total_win_user, total_loss_user, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stat_period_range_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stat_period_range_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.StatPeriodRangeInfo" useGeneratedKeys="true">
    insert into stat_period_range_info (date, arena_id, platform, 
      range_id, sum_income, win_income, 
      spending_ratio, play_user, win_user, 
      total_win_user, total_loss_user, create_time, 
      update_time)
    values (#{date,jdbcType=DATE}, #{arenaId,jdbcType=INTEGER}, #{platform,jdbcType=VARCHAR}, 
      #{rangeId,jdbcType=INTEGER}, #{sumIncome,jdbcType=INTEGER}, #{winIncome,jdbcType=INTEGER}, 
      #{spendingRatio,jdbcType=VARCHAR}, #{playUser,jdbcType=INTEGER}, #{winUser,jdbcType=INTEGER}, 
      #{totalWinUser,jdbcType=INTEGER}, #{totalLossUser,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.StatPeriodRangeInfo" useGeneratedKeys="true">
    insert into stat_period_range_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="date != null">
        date,
      </if>
      <if test="arenaId != null">
        arena_id,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="rangeId != null">
        range_id,
      </if>
      <if test="sumIncome != null">
        sum_income,
      </if>
      <if test="winIncome != null">
        win_income,
      </if>
      <if test="spendingRatio != null">
        spending_ratio,
      </if>
      <if test="playUser != null">
        play_user,
      </if>
      <if test="winUser != null">
        win_user,
      </if>
      <if test="totalWinUser != null">
        total_win_user,
      </if>
      <if test="totalLossUser != null">
        total_loss_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="arenaId != null">
        #{arenaId,jdbcType=INTEGER},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=VARCHAR},
      </if>
      <if test="rangeId != null">
        #{rangeId,jdbcType=INTEGER},
      </if>
      <if test="sumIncome != null">
        #{sumIncome,jdbcType=INTEGER},
      </if>
      <if test="winIncome != null">
        #{winIncome,jdbcType=INTEGER},
      </if>
      <if test="spendingRatio != null">
        #{spendingRatio,jdbcType=VARCHAR},
      </if>
      <if test="playUser != null">
        #{playUser,jdbcType=INTEGER},
      </if>
      <if test="winUser != null">
        #{winUser,jdbcType=INTEGER},
      </if>
      <if test="totalWinUser != null">
        #{totalWinUser,jdbcType=INTEGER},
      </if>
      <if test="totalLossUser != null">
        #{totalLossUser,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.hd.persist.openmode.model.StatPeriodRangeInfo">
    update stat_period_range_info
    <set>
      <if test="date != null">
        date = #{date,jdbcType=DATE},
      </if>
      <if test="arenaId != null">
        arena_id = #{arenaId,jdbcType=INTEGER},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=VARCHAR},
      </if>
      <if test="rangeId != null">
        range_id = #{rangeId,jdbcType=INTEGER},
      </if>
      <if test="sumIncome != null">
        sum_income = #{sumIncome,jdbcType=INTEGER},
      </if>
      <if test="winIncome != null">
        win_income = #{winIncome,jdbcType=INTEGER},
      </if>
      <if test="spendingRatio != null">
        spending_ratio = #{spendingRatio,jdbcType=VARCHAR},
      </if>
      <if test="playUser != null">
        play_user = #{playUser,jdbcType=INTEGER},
      </if>
      <if test="winUser != null">
        win_user = #{winUser,jdbcType=INTEGER},
      </if>
      <if test="totalWinUser != null">
        total_win_user = #{totalWinUser,jdbcType=INTEGER},
      </if>
      <if test="totalLossUser != null">
        total_loss_user = #{totalLossUser,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.hd.persist.openmode.model.StatPeriodRangeInfo">
    update stat_period_range_info
    set date = #{date,jdbcType=DATE},
      arena_id = #{arenaId,jdbcType=INTEGER},
      platform = #{platform,jdbcType=VARCHAR},
      range_id = #{rangeId,jdbcType=INTEGER},
      sum_income = #{sumIncome,jdbcType=INTEGER},
      win_income = #{winIncome,jdbcType=INTEGER},
      spending_ratio = #{spendingRatio,jdbcType=VARCHAR},
      play_user = #{playUser,jdbcType=INTEGER},
      win_user = #{winUser,jdbcType=INTEGER},
      total_win_user = #{totalWinUser,jdbcType=INTEGER},
      total_loss_user = #{totalLossUser,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
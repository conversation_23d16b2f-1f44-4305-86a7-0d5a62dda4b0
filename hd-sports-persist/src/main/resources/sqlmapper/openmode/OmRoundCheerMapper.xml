<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.hd.persist.openmode.mapper.OmRoundCheerMapper">
  <resultMap id="BaseResultMap" type="com.yy.hd.persist.openmode.model.OmRoundCheer">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="seq_id" jdbcType="CHAR" property="seqId" />
    <result column="round_id" jdbcType="BIGINT" property="roundId" />
    <result column="candidate_id" jdbcType="BIGINT" property="candidateId" />
    <result column="uid" jdbcType="BIGINT" property="uid" />
    <result column="biz_id" jdbcType="INTEGER" property="bizId" />
    <result column="host_name" jdbcType="CHAR" property="hostName" />
    <result column="sid" jdbcType="BIGINT" property="sid" />
    <result column="ssid" jdbcType="BIGINT" property="ssid" />
    <result column="game_type" jdbcType="INTEGER" property="gameType" />
    <result column="compere_uid" jdbcType="BIGINT" property="compereUid" />
    <result column="target_uid" jdbcType="BIGINT" property="targetUid" />
    <result column="profit_uid" jdbcType="BIGINT" property="profitUid" />
    <result column="fruit_count" jdbcType="BIGINT" property="fruitCount" />
    <result column="cheer_value" jdbcType="BIGINT" property="cheerValue" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="client_expand" jdbcType="VARCHAR" property="clientExpand" />
  </resultMap>
  <sql id="Base_Column_List">
    id, seq_id, round_id, candidate_id, uid, biz_id, host_name, sid, ssid, game_type, 
    compere_uid, target_uid, profit_uid, fruit_count, cheer_value, create_time, update_time, 
    client_expand
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from om_round_cheer
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from om_round_cheer
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.OmRoundCheer" useGeneratedKeys="true">
    insert into om_round_cheer (seq_id, round_id, candidate_id, 
      uid, biz_id, host_name, sid, 
      ssid, game_type, compere_uid, 
      target_uid, profit_uid, fruit_count, 
      cheer_value, create_time, update_time, 
      client_expand)
    values (#{seqId,jdbcType=CHAR}, #{roundId,jdbcType=BIGINT}, #{candidateId,jdbcType=BIGINT}, 
      #{uid,jdbcType=BIGINT}, #{bizId,jdbcType=INTEGER}, #{hostName,jdbcType=CHAR}, #{sid,jdbcType=BIGINT}, 
      #{ssid,jdbcType=BIGINT}, #{gameType,jdbcType=INTEGER}, #{compereUid,jdbcType=BIGINT}, 
      #{targetUid,jdbcType=BIGINT}, #{profitUid,jdbcType=BIGINT}, #{fruitCount,jdbcType=BIGINT}, 
      #{cheerValue,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{clientExpand,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.OmRoundCheer" useGeneratedKeys="true">
    insert into om_round_cheer
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="seqId != null">
        seq_id,
      </if>
      <if test="roundId != null">
        round_id,
      </if>
      <if test="candidateId != null">
        candidate_id,
      </if>
      <if test="uid != null">
        uid,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="hostName != null">
        host_name,
      </if>
      <if test="sid != null">
        sid,
      </if>
      <if test="ssid != null">
        ssid,
      </if>
      <if test="gameType != null">
        game_type,
      </if>
      <if test="compereUid != null">
        compere_uid,
      </if>
      <if test="targetUid != null">
        target_uid,
      </if>
      <if test="profitUid != null">
        profit_uid,
      </if>
      <if test="fruitCount != null">
        fruit_count,
      </if>
      <if test="cheerValue != null">
        cheer_value,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="clientExpand != null">
        client_expand,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="seqId != null">
        #{seqId,jdbcType=CHAR},
      </if>
      <if test="roundId != null">
        #{roundId,jdbcType=BIGINT},
      </if>
      <if test="candidateId != null">
        #{candidateId,jdbcType=BIGINT},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=INTEGER},
      </if>
      <if test="hostName != null">
        #{hostName,jdbcType=CHAR},
      </if>
      <if test="sid != null">
        #{sid,jdbcType=BIGINT},
      </if>
      <if test="ssid != null">
        #{ssid,jdbcType=BIGINT},
      </if>
      <if test="gameType != null">
        #{gameType,jdbcType=INTEGER},
      </if>
      <if test="compereUid != null">
        #{compereUid,jdbcType=BIGINT},
      </if>
      <if test="targetUid != null">
        #{targetUid,jdbcType=BIGINT},
      </if>
      <if test="profitUid != null">
        #{profitUid,jdbcType=BIGINT},
      </if>
      <if test="fruitCount != null">
        #{fruitCount,jdbcType=BIGINT},
      </if>
      <if test="cheerValue != null">
        #{cheerValue,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="clientExpand != null">
        #{clientExpand,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.hd.persist.openmode.model.OmRoundCheer">
    update om_round_cheer
    <set>
      <if test="seqId != null">
        seq_id = #{seqId,jdbcType=CHAR},
      </if>
      <if test="roundId != null">
        round_id = #{roundId,jdbcType=BIGINT},
      </if>
      <if test="candidateId != null">
        candidate_id = #{candidateId,jdbcType=BIGINT},
      </if>
      <if test="uid != null">
        uid = #{uid,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=INTEGER},
      </if>
      <if test="hostName != null">
        host_name = #{hostName,jdbcType=CHAR},
      </if>
      <if test="sid != null">
        sid = #{sid,jdbcType=BIGINT},
      </if>
      <if test="ssid != null">
        ssid = #{ssid,jdbcType=BIGINT},
      </if>
      <if test="gameType != null">
        game_type = #{gameType,jdbcType=INTEGER},
      </if>
      <if test="compereUid != null">
        compere_uid = #{compereUid,jdbcType=BIGINT},
      </if>
      <if test="targetUid != null">
        target_uid = #{targetUid,jdbcType=BIGINT},
      </if>
      <if test="profitUid != null">
        profit_uid = #{profitUid,jdbcType=BIGINT},
      </if>
      <if test="fruitCount != null">
        fruit_count = #{fruitCount,jdbcType=BIGINT},
      </if>
      <if test="cheerValue != null">
        cheer_value = #{cheerValue,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="clientExpand != null">
        client_expand = #{clientExpand,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.hd.persist.openmode.model.OmRoundCheer">
    update om_round_cheer
    set seq_id = #{seqId,jdbcType=CHAR},
      round_id = #{roundId,jdbcType=BIGINT},
      candidate_id = #{candidateId,jdbcType=BIGINT},
      uid = #{uid,jdbcType=BIGINT},
      biz_id = #{bizId,jdbcType=INTEGER},
      host_name = #{hostName,jdbcType=CHAR},
      sid = #{sid,jdbcType=BIGINT},
      ssid = #{ssid,jdbcType=BIGINT},
      game_type = #{gameType,jdbcType=INTEGER},
      compere_uid = #{compereUid,jdbcType=BIGINT},
      target_uid = #{targetUid,jdbcType=BIGINT},
      profit_uid = #{profitUid,jdbcType=BIGINT},
      fruit_count = #{fruitCount,jdbcType=BIGINT},
      cheer_value = #{cheerValue,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      client_expand = #{clientExpand,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
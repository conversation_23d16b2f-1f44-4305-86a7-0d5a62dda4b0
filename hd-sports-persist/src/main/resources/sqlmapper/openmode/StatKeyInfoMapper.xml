<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.hd.persist.openmode.mapper.StatKeyInfoMapper">
  <resultMap id="BaseResultMap" type="com.yy.hd.persist.openmode.model.StatKeyInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="arena_id" jdbcType="INTEGER" property="arenaId" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="frag_count" jdbcType="INTEGER" property="fragCount" />
    <result column="gift_spending" jdbcType="INTEGER" property="giftSpending" />
    <result column="total_spending" jdbcType="INTEGER" property="totalSpending" />
    <result column="play_count" jdbcType="INTEGER" property="playCount" />
    <result column="total_win_user" jdbcType="INTEGER" property="totalWinUser" />
    <result column="win_count" jdbcType="INTEGER" property="winCount" />
    <result column="lose_count" jdbcType="INTEGER" property="loseCount" />
    <result column="total_props_value" jdbcType="INTEGER" property="totalPropsValue" />
    <result column="sum_fruit" jdbcType="INTEGER" property="sumFruit" />
    <result column="spending_user_ratio" jdbcType="VARCHAR" property="spendingUserRatio" />
    <result column="spending_fruit_ratio" jdbcType="VARCHAR" property="spendingFruitRatio" />
    <result column="gift_fruit_ratio" jdbcType="VARCHAR" property="giftFruitRatio" />
    <result column="fruit_gift_ratio" jdbcType="VARCHAR" property="fruitGiftRatio" />
    <result column="fruit_spending_ratio" jdbcType="VARCHAR" property="fruitSpendingRatio" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, date, arena_id, platform, frag_count, gift_spending, total_spending, play_count, 
    total_win_user, win_count, lose_count, total_props_value, sum_fruit, spending_user_ratio, 
    spending_fruit_ratio, gift_fruit_ratio, fruit_gift_ratio, fruit_spending_ratio, create_time, 
    update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stat_key_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stat_key_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.StatKeyInfo" useGeneratedKeys="true">
    insert into stat_key_info (date, arena_id, platform, 
      frag_count, gift_spending, total_spending, 
      play_count, total_win_user, win_count, 
      lose_count, total_props_value, sum_fruit, 
      spending_user_ratio, spending_fruit_ratio, 
      gift_fruit_ratio, fruit_gift_ratio, fruit_spending_ratio, 
      create_time, update_time)
    values (#{date,jdbcType=DATE}, #{arenaId,jdbcType=INTEGER}, #{platform,jdbcType=VARCHAR}, 
      #{fragCount,jdbcType=INTEGER}, #{giftSpending,jdbcType=INTEGER}, #{totalSpending,jdbcType=INTEGER}, 
      #{playCount,jdbcType=INTEGER}, #{totalWinUser,jdbcType=INTEGER}, #{winCount,jdbcType=INTEGER}, 
      #{loseCount,jdbcType=INTEGER}, #{totalPropsValue,jdbcType=INTEGER}, #{sumFruit,jdbcType=INTEGER}, 
      #{spendingUserRatio,jdbcType=VARCHAR}, #{spendingFruitRatio,jdbcType=VARCHAR}, 
      #{giftFruitRatio,jdbcType=VARCHAR}, #{fruitGiftRatio,jdbcType=VARCHAR}, #{fruitSpendingRatio,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.StatKeyInfo" useGeneratedKeys="true">
    insert into stat_key_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="date != null">
        date,
      </if>
      <if test="arenaId != null">
        arena_id,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="fragCount != null">
        frag_count,
      </if>
      <if test="giftSpending != null">
        gift_spending,
      </if>
      <if test="totalSpending != null">
        total_spending,
      </if>
      <if test="playCount != null">
        play_count,
      </if>
      <if test="totalWinUser != null">
        total_win_user,
      </if>
      <if test="winCount != null">
        win_count,
      </if>
      <if test="loseCount != null">
        lose_count,
      </if>
      <if test="totalPropsValue != null">
        total_props_value,
      </if>
      <if test="sumFruit != null">
        sum_fruit,
      </if>
      <if test="spendingUserRatio != null">
        spending_user_ratio,
      </if>
      <if test="spendingFruitRatio != null">
        spending_fruit_ratio,
      </if>
      <if test="giftFruitRatio != null">
        gift_fruit_ratio,
      </if>
      <if test="fruitGiftRatio != null">
        fruit_gift_ratio,
      </if>
      <if test="fruitSpendingRatio != null">
        fruit_spending_ratio,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="arenaId != null">
        #{arenaId,jdbcType=INTEGER},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=VARCHAR},
      </if>
      <if test="fragCount != null">
        #{fragCount,jdbcType=INTEGER},
      </if>
      <if test="giftSpending != null">
        #{giftSpending,jdbcType=INTEGER},
      </if>
      <if test="totalSpending != null">
        #{totalSpending,jdbcType=INTEGER},
      </if>
      <if test="playCount != null">
        #{playCount,jdbcType=INTEGER},
      </if>
      <if test="totalWinUser != null">
        #{totalWinUser,jdbcType=INTEGER},
      </if>
      <if test="winCount != null">
        #{winCount,jdbcType=INTEGER},
      </if>
      <if test="loseCount != null">
        #{loseCount,jdbcType=INTEGER},
      </if>
      <if test="totalPropsValue != null">
        #{totalPropsValue,jdbcType=INTEGER},
      </if>
      <if test="sumFruit != null">
        #{sumFruit,jdbcType=INTEGER},
      </if>
      <if test="spendingUserRatio != null">
        #{spendingUserRatio,jdbcType=VARCHAR},
      </if>
      <if test="spendingFruitRatio != null">
        #{spendingFruitRatio,jdbcType=VARCHAR},
      </if>
      <if test="giftFruitRatio != null">
        #{giftFruitRatio,jdbcType=VARCHAR},
      </if>
      <if test="fruitGiftRatio != null">
        #{fruitGiftRatio,jdbcType=VARCHAR},
      </if>
      <if test="fruitSpendingRatio != null">
        #{fruitSpendingRatio,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.hd.persist.openmode.model.StatKeyInfo">
    update stat_key_info
    <set>
      <if test="date != null">
        date = #{date,jdbcType=DATE},
      </if>
      <if test="arenaId != null">
        arena_id = #{arenaId,jdbcType=INTEGER},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=VARCHAR},
      </if>
      <if test="fragCount != null">
        frag_count = #{fragCount,jdbcType=INTEGER},
      </if>
      <if test="giftSpending != null">
        gift_spending = #{giftSpending,jdbcType=INTEGER},
      </if>
      <if test="totalSpending != null">
        total_spending = #{totalSpending,jdbcType=INTEGER},
      </if>
      <if test="playCount != null">
        play_count = #{playCount,jdbcType=INTEGER},
      </if>
      <if test="totalWinUser != null">
        total_win_user = #{totalWinUser,jdbcType=INTEGER},
      </if>
      <if test="winCount != null">
        win_count = #{winCount,jdbcType=INTEGER},
      </if>
      <if test="loseCount != null">
        lose_count = #{loseCount,jdbcType=INTEGER},
      </if>
      <if test="totalPropsValue != null">
        total_props_value = #{totalPropsValue,jdbcType=INTEGER},
      </if>
      <if test="sumFruit != null">
        sum_fruit = #{sumFruit,jdbcType=INTEGER},
      </if>
      <if test="spendingUserRatio != null">
        spending_user_ratio = #{spendingUserRatio,jdbcType=VARCHAR},
      </if>
      <if test="spendingFruitRatio != null">
        spending_fruit_ratio = #{spendingFruitRatio,jdbcType=VARCHAR},
      </if>
      <if test="giftFruitRatio != null">
        gift_fruit_ratio = #{giftFruitRatio,jdbcType=VARCHAR},
      </if>
      <if test="fruitGiftRatio != null">
        fruit_gift_ratio = #{fruitGiftRatio,jdbcType=VARCHAR},
      </if>
      <if test="fruitSpendingRatio != null">
        fruit_spending_ratio = #{fruitSpendingRatio,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.hd.persist.openmode.model.StatKeyInfo">
    update stat_key_info
    set date = #{date,jdbcType=DATE},
      arena_id = #{arenaId,jdbcType=INTEGER},
      platform = #{platform,jdbcType=VARCHAR},
      frag_count = #{fragCount,jdbcType=INTEGER},
      gift_spending = #{giftSpending,jdbcType=INTEGER},
      total_spending = #{totalSpending,jdbcType=INTEGER},
      play_count = #{playCount,jdbcType=INTEGER},
      total_win_user = #{totalWinUser,jdbcType=INTEGER},
      win_count = #{winCount,jdbcType=INTEGER},
      lose_count = #{loseCount,jdbcType=INTEGER},
      total_props_value = #{totalPropsValue,jdbcType=INTEGER},
      sum_fruit = #{sumFruit,jdbcType=INTEGER},
      spending_user_ratio = #{spendingUserRatio,jdbcType=VARCHAR},
      spending_fruit_ratio = #{spendingFruitRatio,jdbcType=VARCHAR},
      gift_fruit_ratio = #{giftFruitRatio,jdbcType=VARCHAR},
      fruit_gift_ratio = #{fruitGiftRatio,jdbcType=VARCHAR},
      fruit_spending_ratio = #{fruitSpendingRatio,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.hd.persist.openmode.mapper.OmCandidateMapper">
  <resultMap id="BaseResultMap" type="com.yy.hd.persist.openmode.model.OmCandidate">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="award_desc" jdbcType="VARCHAR" property="awardDesc" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="reward_rate" jdbcType="INTEGER" property="rewardRate" />
    <result column="probability" jdbcType="BIGINT" property="probability" />
  </resultMap>
  <sql id="Base_Column_List">
    id, name, award_desc, icon, reward_rate, probability
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from om_candidate
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from om_candidate
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.OmCandidate" useGeneratedKeys="true">
    insert into om_candidate (name, award_desc, icon, 
      reward_rate, probability)
    values (#{name,jdbcType=VARCHAR}, #{awardDesc,jdbcType=VARCHAR}, #{icon,jdbcType=VARCHAR}, 
      #{rewardRate,jdbcType=INTEGER}, #{probability,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.OmCandidate" useGeneratedKeys="true">
    insert into om_candidate
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="awardDesc != null">
        award_desc,
      </if>
      <if test="icon != null">
        icon,
      </if>
      <if test="rewardRate != null">
        reward_rate,
      </if>
      <if test="probability != null">
        probability,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="awardDesc != null">
        #{awardDesc,jdbcType=VARCHAR},
      </if>
      <if test="icon != null">
        #{icon,jdbcType=VARCHAR},
      </if>
      <if test="rewardRate != null">
        #{rewardRate,jdbcType=INTEGER},
      </if>
      <if test="probability != null">
        #{probability,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.hd.persist.openmode.model.OmCandidate">
    update om_candidate
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="awardDesc != null">
        award_desc = #{awardDesc,jdbcType=VARCHAR},
      </if>
      <if test="icon != null">
        icon = #{icon,jdbcType=VARCHAR},
      </if>
      <if test="rewardRate != null">
        reward_rate = #{rewardRate,jdbcType=INTEGER},
      </if>
      <if test="probability != null">
        probability = #{probability,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.hd.persist.openmode.model.OmCandidate">
    update om_candidate
    set name = #{name,jdbcType=VARCHAR},
      award_desc = #{awardDesc,jdbcType=VARCHAR},
      icon = #{icon,jdbcType=VARCHAR},
      reward_rate = #{rewardRate,jdbcType=INTEGER},
      probability = #{probability,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
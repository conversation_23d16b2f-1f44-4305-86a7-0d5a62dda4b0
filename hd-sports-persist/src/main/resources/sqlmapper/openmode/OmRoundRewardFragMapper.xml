<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.hd.persist.openmode.mapper.OmRoundRewardFragMapper">
  <resultMap id="BaseResultMap" type="com.yy.hd.persist.openmode.model.OmRoundRewardFrag">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="seq_id" jdbcType="CHAR" property="seqId" />
    <result column="round_id" jdbcType="BIGINT" property="roundId" />
    <result column="uid" jdbcType="BIGINT" property="uid" />
    <result column="biz_id" jdbcType="INTEGER" property="bizId" />
    <result column="sid" jdbcType="BIGINT" property="sid" />
    <result column="ssid" jdbcType="BIGINT" property="ssid" />
    <result column="frag_count" jdbcType="BIGINT" property="fragCount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, seq_id, round_id, uid, biz_id, sid, ssid, frag_count, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from om_round_reward_frag
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from om_round_reward_frag
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.OmRoundRewardFrag" useGeneratedKeys="true">
    insert into om_round_reward_frag (seq_id, round_id, uid, 
      biz_id, sid, ssid, frag_count, 
      create_time)
    values (#{seqId,jdbcType=CHAR}, #{roundId,jdbcType=BIGINT}, #{uid,jdbcType=BIGINT}, 
      #{bizId,jdbcType=INTEGER}, #{sid,jdbcType=BIGINT}, #{ssid,jdbcType=BIGINT}, #{fragCount,jdbcType=BIGINT}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.OmRoundRewardFrag" useGeneratedKeys="true">
    insert into om_round_reward_frag
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="seqId != null">
        seq_id,
      </if>
      <if test="roundId != null">
        round_id,
      </if>
      <if test="uid != null">
        uid,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="sid != null">
        sid,
      </if>
      <if test="ssid != null">
        ssid,
      </if>
      <if test="fragCount != null">
        frag_count,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="seqId != null">
        #{seqId,jdbcType=CHAR},
      </if>
      <if test="roundId != null">
        #{roundId,jdbcType=BIGINT},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=INTEGER},
      </if>
      <if test="sid != null">
        #{sid,jdbcType=BIGINT},
      </if>
      <if test="ssid != null">
        #{ssid,jdbcType=BIGINT},
      </if>
      <if test="fragCount != null">
        #{fragCount,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.hd.persist.openmode.model.OmRoundRewardFrag">
    update om_round_reward_frag
    <set>
      <if test="seqId != null">
        seq_id = #{seqId,jdbcType=CHAR},
      </if>
      <if test="roundId != null">
        round_id = #{roundId,jdbcType=BIGINT},
      </if>
      <if test="uid != null">
        uid = #{uid,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=INTEGER},
      </if>
      <if test="sid != null">
        sid = #{sid,jdbcType=BIGINT},
      </if>
      <if test="ssid != null">
        ssid = #{ssid,jdbcType=BIGINT},
      </if>
      <if test="fragCount != null">
        frag_count = #{fragCount,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.hd.persist.openmode.model.OmRoundRewardFrag">
    update om_round_reward_frag
    set seq_id = #{seqId,jdbcType=CHAR},
      round_id = #{roundId,jdbcType=BIGINT},
      uid = #{uid,jdbcType=BIGINT},
      biz_id = #{bizId,jdbcType=INTEGER},
      sid = #{sid,jdbcType=BIGINT},
      ssid = #{ssid,jdbcType=BIGINT},
      frag_count = #{fragCount,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
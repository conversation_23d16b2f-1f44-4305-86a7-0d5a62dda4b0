<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.hd.persist.openmode.mapper.StatTopUserInfoMapper">
  <resultMap id="BaseResultMap" type="com.yy.hd.persist.openmode.model.StatTopUserInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="uid" jdbcType="BIGINT" property="uid" />
    <result column="seal_amount" jdbcType="BIGINT" property="sealAmount" />
    <result column="play_amount" jdbcType="BIGINT" property="playAmount" />
    <result column="award_amount" jdbcType="BIGINT" property="awardAmount" />
    <result column="play_count" jdbcType="BIGINT" property="playCount" />
    <result column="win_count" jdbcType="BIGINT" property="winCount" />
    <result column="prefer_position" jdbcType="VARCHAR" property="preferPosition" />
    <result column="prefer_position_count" jdbcType="BIGINT" property="preferPositionCount" />
    <result column="prefer_position_amount" jdbcType="BIGINT" property="preferPositionAmount" />
    <result column="play_ratio" jdbcType="VARCHAR" property="playRatio" />
    <result column="amount_ratio" jdbcType="VARCHAR" property="amountRatio" />
    <result column="top_sid" jdbcType="VARCHAR" property="topSid" />
    <result column="top_sid_amount" jdbcType="BIGINT" property="topSidAmount" />
    <result column="top_sid_ratio" jdbcType="VARCHAR" property="topSidRatio" />
    <result column="top_ssid" jdbcType="VARCHAR" property="topSsid" />
    <result column="top_ssid_amount" jdbcType="BIGINT" property="topSsidAmount" />
    <result column="top_ssid_ratio" jdbcType="VARCHAR" property="topSsidRatio" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, date, uid, seal_amount, play_amount, award_amount, play_count, win_count, prefer_position, 
    prefer_position_count, prefer_position_amount, play_ratio, amount_ratio, top_sid, 
    top_sid_amount, top_sid_ratio, top_ssid, top_ssid_amount, top_ssid_ratio, create_time, 
    update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stat_top_user_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stat_top_user_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.StatTopUserInfo" useGeneratedKeys="true">
    insert into stat_top_user_info (date, uid, seal_amount, 
      play_amount, award_amount, play_count, 
      win_count, prefer_position, prefer_position_count, 
      prefer_position_amount, play_ratio, amount_ratio, 
      top_sid, top_sid_amount, top_sid_ratio, 
      top_ssid, top_ssid_amount, top_ssid_ratio, 
      create_time, update_time)
    values (#{date,jdbcType=DATE}, #{uid,jdbcType=BIGINT}, #{sealAmount,jdbcType=BIGINT}, 
      #{playAmount,jdbcType=BIGINT}, #{awardAmount,jdbcType=BIGINT}, #{playCount,jdbcType=BIGINT}, 
      #{winCount,jdbcType=BIGINT}, #{preferPosition,jdbcType=VARCHAR}, #{preferPositionCount,jdbcType=BIGINT}, 
      #{preferPositionAmount,jdbcType=BIGINT}, #{playRatio,jdbcType=VARCHAR}, #{amountRatio,jdbcType=VARCHAR}, 
      #{topSid,jdbcType=VARCHAR}, #{topSidAmount,jdbcType=BIGINT}, #{topSidRatio,jdbcType=VARCHAR}, 
      #{topSsid,jdbcType=VARCHAR}, #{topSsidAmount,jdbcType=BIGINT}, #{topSsidRatio,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.StatTopUserInfo" useGeneratedKeys="true">
    insert into stat_top_user_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="date != null">
        date,
      </if>
      <if test="uid != null">
        uid,
      </if>
      <if test="sealAmount != null">
        seal_amount,
      </if>
      <if test="playAmount != null">
        play_amount,
      </if>
      <if test="awardAmount != null">
        award_amount,
      </if>
      <if test="playCount != null">
        play_count,
      </if>
      <if test="winCount != null">
        win_count,
      </if>
      <if test="preferPosition != null">
        prefer_position,
      </if>
      <if test="preferPositionCount != null">
        prefer_position_count,
      </if>
      <if test="preferPositionAmount != null">
        prefer_position_amount,
      </if>
      <if test="playRatio != null">
        play_ratio,
      </if>
      <if test="amountRatio != null">
        amount_ratio,
      </if>
      <if test="topSid != null">
        top_sid,
      </if>
      <if test="topSidAmount != null">
        top_sid_amount,
      </if>
      <if test="topSidRatio != null">
        top_sid_ratio,
      </if>
      <if test="topSsid != null">
        top_ssid,
      </if>
      <if test="topSsidAmount != null">
        top_ssid_amount,
      </if>
      <if test="topSsidRatio != null">
        top_ssid_ratio,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=BIGINT},
      </if>
      <if test="sealAmount != null">
        #{sealAmount,jdbcType=BIGINT},
      </if>
      <if test="playAmount != null">
        #{playAmount,jdbcType=BIGINT},
      </if>
      <if test="awardAmount != null">
        #{awardAmount,jdbcType=BIGINT},
      </if>
      <if test="playCount != null">
        #{playCount,jdbcType=BIGINT},
      </if>
      <if test="winCount != null">
        #{winCount,jdbcType=BIGINT},
      </if>
      <if test="preferPosition != null">
        #{preferPosition,jdbcType=VARCHAR},
      </if>
      <if test="preferPositionCount != null">
        #{preferPositionCount,jdbcType=BIGINT},
      </if>
      <if test="preferPositionAmount != null">
        #{preferPositionAmount,jdbcType=BIGINT},
      </if>
      <if test="playRatio != null">
        #{playRatio,jdbcType=VARCHAR},
      </if>
      <if test="amountRatio != null">
        #{amountRatio,jdbcType=VARCHAR},
      </if>
      <if test="topSid != null">
        #{topSid,jdbcType=VARCHAR},
      </if>
      <if test="topSidAmount != null">
        #{topSidAmount,jdbcType=BIGINT},
      </if>
      <if test="topSidRatio != null">
        #{topSidRatio,jdbcType=VARCHAR},
      </if>
      <if test="topSsid != null">
        #{topSsid,jdbcType=VARCHAR},
      </if>
      <if test="topSsidAmount != null">
        #{topSsidAmount,jdbcType=BIGINT},
      </if>
      <if test="topSsidRatio != null">
        #{topSsidRatio,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.hd.persist.openmode.model.StatTopUserInfo">
    update stat_top_user_info
    <set>
      <if test="date != null">
        date = #{date,jdbcType=DATE},
      </if>
      <if test="uid != null">
        uid = #{uid,jdbcType=BIGINT},
      </if>
      <if test="sealAmount != null">
        seal_amount = #{sealAmount,jdbcType=BIGINT},
      </if>
      <if test="playAmount != null">
        play_amount = #{playAmount,jdbcType=BIGINT},
      </if>
      <if test="awardAmount != null">
        award_amount = #{awardAmount,jdbcType=BIGINT},
      </if>
      <if test="playCount != null">
        play_count = #{playCount,jdbcType=BIGINT},
      </if>
      <if test="winCount != null">
        win_count = #{winCount,jdbcType=BIGINT},
      </if>
      <if test="preferPosition != null">
        prefer_position = #{preferPosition,jdbcType=VARCHAR},
      </if>
      <if test="preferPositionCount != null">
        prefer_position_count = #{preferPositionCount,jdbcType=BIGINT},
      </if>
      <if test="preferPositionAmount != null">
        prefer_position_amount = #{preferPositionAmount,jdbcType=BIGINT},
      </if>
      <if test="playRatio != null">
        play_ratio = #{playRatio,jdbcType=VARCHAR},
      </if>
      <if test="amountRatio != null">
        amount_ratio = #{amountRatio,jdbcType=VARCHAR},
      </if>
      <if test="topSid != null">
        top_sid = #{topSid,jdbcType=VARCHAR},
      </if>
      <if test="topSidAmount != null">
        top_sid_amount = #{topSidAmount,jdbcType=BIGINT},
      </if>
      <if test="topSidRatio != null">
        top_sid_ratio = #{topSidRatio,jdbcType=VARCHAR},
      </if>
      <if test="topSsid != null">
        top_ssid = #{topSsid,jdbcType=VARCHAR},
      </if>
      <if test="topSsidAmount != null">
        top_ssid_amount = #{topSsidAmount,jdbcType=BIGINT},
      </if>
      <if test="topSsidRatio != null">
        top_ssid_ratio = #{topSsidRatio,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.hd.persist.openmode.model.StatTopUserInfo">
    update stat_top_user_info
    set date = #{date,jdbcType=DATE},
      uid = #{uid,jdbcType=BIGINT},
      seal_amount = #{sealAmount,jdbcType=BIGINT},
      play_amount = #{playAmount,jdbcType=BIGINT},
      award_amount = #{awardAmount,jdbcType=BIGINT},
      play_count = #{playCount,jdbcType=BIGINT},
      win_count = #{winCount,jdbcType=BIGINT},
      prefer_position = #{preferPosition,jdbcType=VARCHAR},
      prefer_position_count = #{preferPositionCount,jdbcType=BIGINT},
      prefer_position_amount = #{preferPositionAmount,jdbcType=BIGINT},
      play_ratio = #{playRatio,jdbcType=VARCHAR},
      amount_ratio = #{amountRatio,jdbcType=VARCHAR},
      top_sid = #{topSid,jdbcType=VARCHAR},
      top_sid_amount = #{topSidAmount,jdbcType=BIGINT},
      top_sid_ratio = #{topSidRatio,jdbcType=VARCHAR},
      top_ssid = #{topSsid,jdbcType=VARCHAR},
      top_ssid_amount = #{topSsidAmount,jdbcType=BIGINT},
      top_ssid_ratio = #{topSsidRatio,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
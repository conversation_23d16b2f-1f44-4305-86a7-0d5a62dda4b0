<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.hd.persist.openmode.mapper.OmRoundMapper">
  <resultMap id="BaseResultMap" type="com.yy.hd.persist.openmode.model.OmRound">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="round_id" jdbcType="BIGINT" property="roundId" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="init_offset" jdbcType="VARCHAR" property="initOffset" />
    <result column="first_candidate_id" jdbcType="BIGINT" property="firstCandidateId" />
    <result column="other_candidate_ids" jdbcType="VARCHAR" property="otherCandidateIds" />
    <result column="champion_uid" jdbcType="BIGINT" property="championUid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.yy.hd.persist.openmode.model.OmRound">
    <result column="config_snapshot" jdbcType="LONGVARCHAR" property="configSnapshot" />
  </resultMap>
  <sql id="Base_Column_List">
    id, round_id, start_time, end_time, status, init_offset, first_candidate_id, other_candidate_ids, 
    champion_uid, create_time
  </sql>
  <sql id="Blob_Column_List">
    config_snapshot
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from om_round
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from om_round
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.OmRound" useGeneratedKeys="true">
    insert into om_round (round_id, start_time, end_time, 
      status, init_offset, first_candidate_id, 
      other_candidate_ids, champion_uid, create_time, 
      config_snapshot)
    values (#{roundId,jdbcType=BIGINT}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=INTEGER}, #{initOffset,jdbcType=VARCHAR}, #{firstCandidateId,jdbcType=BIGINT}, 
      #{otherCandidateIds,jdbcType=VARCHAR}, #{championUid,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{configSnapshot,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.OmRound" useGeneratedKeys="true">
    insert into om_round
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="roundId != null">
        round_id,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="initOffset != null">
        init_offset,
      </if>
      <if test="firstCandidateId != null">
        first_candidate_id,
      </if>
      <if test="otherCandidateIds != null">
        other_candidate_ids,
      </if>
      <if test="championUid != null">
        champion_uid,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="configSnapshot != null">
        config_snapshot,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="roundId != null">
        #{roundId,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="initOffset != null">
        #{initOffset,jdbcType=VARCHAR},
      </if>
      <if test="firstCandidateId != null">
        #{firstCandidateId,jdbcType=BIGINT},
      </if>
      <if test="otherCandidateIds != null">
        #{otherCandidateIds,jdbcType=VARCHAR},
      </if>
      <if test="championUid != null">
        #{championUid,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="configSnapshot != null">
        #{configSnapshot,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.hd.persist.openmode.model.OmRound">
    update om_round
    <set>
      <if test="roundId != null">
        round_id = #{roundId,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="initOffset != null">
        init_offset = #{initOffset,jdbcType=VARCHAR},
      </if>
      <if test="firstCandidateId != null">
        first_candidate_id = #{firstCandidateId,jdbcType=BIGINT},
      </if>
      <if test="otherCandidateIds != null">
        other_candidate_ids = #{otherCandidateIds,jdbcType=VARCHAR},
      </if>
      <if test="championUid != null">
        champion_uid = #{championUid,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="configSnapshot != null">
        config_snapshot = #{configSnapshot,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.yy.hd.persist.openmode.model.OmRound">
    update om_round
    set round_id = #{roundId,jdbcType=BIGINT},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=INTEGER},
      init_offset = #{initOffset,jdbcType=VARCHAR},
      first_candidate_id = #{firstCandidateId,jdbcType=BIGINT},
      other_candidate_ids = #{otherCandidateIds,jdbcType=VARCHAR},
      champion_uid = #{championUid,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      config_snapshot = #{configSnapshot,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.hd.persist.openmode.model.OmRound">
    update om_round
    set round_id = #{roundId,jdbcType=BIGINT},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=INTEGER},
      init_offset = #{initOffset,jdbcType=VARCHAR},
      first_candidate_id = #{firstCandidateId,jdbcType=BIGINT},
      other_candidate_ids = #{otherCandidateIds,jdbcType=VARCHAR},
      champion_uid = #{championUid,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
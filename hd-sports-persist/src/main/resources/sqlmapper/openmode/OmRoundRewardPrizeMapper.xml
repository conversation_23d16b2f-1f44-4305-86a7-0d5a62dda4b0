<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.hd.persist.openmode.mapper.OmRoundRewardPrizeMapper">
  <resultMap id="BaseResultMap" type="com.yy.hd.persist.openmode.model.OmRoundRewardPrize">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="seq_id" jdbcType="CHAR" property="seqId" />
    <result column="round_id" jdbcType="BIGINT" property="roundId" />
    <result column="candidate_id" jdbcType="BIGINT" property="candidateId" />
    <result column="uid" jdbcType="BIGINT" property="uid" />
    <result column="biz_id" jdbcType="INTEGER" property="bizId" />
    <result column="host_name" jdbcType="CHAR" property="hostName" />
    <result column="used_channel" jdbcType="INTEGER" property="usedChannel" />
    <result column="sid" jdbcType="BIGINT" property="sid" />
    <result column="ssid" jdbcType="BIGINT" property="ssid" />
    <result column="total_cheer_prize" jdbcType="BIGINT" property="totalCheerPrize" />
    <result column="target_uid" jdbcType="BIGINT" property="targetUid" />
    <result column="target_nick" jdbcType="BIGINT" property="targetNick" />
    <result column="profit_uid" jdbcType="BIGINT" property="profitUid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="prize_list_json" jdbcType="VARCHAR" property="prizeListJson" />
    <result column="client_expand" jdbcType="VARCHAR" property="clientExpand" />
  </resultMap>
  <sql id="Base_Column_List">
    id, seq_id, round_id, candidate_id, uid, biz_id, host_name, used_channel, sid, ssid, 
    total_cheer_prize, target_uid, target_nick, profit_uid, create_time, prize_list_json, 
    client_expand
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from om_round_reward_prize
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from om_round_reward_prize
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.OmRoundRewardPrize" useGeneratedKeys="true">
    insert into om_round_reward_prize (seq_id, round_id, candidate_id, 
      uid, biz_id, host_name, used_channel, 
      sid, ssid, total_cheer_prize, 
      target_uid, target_nick, profit_uid, 
      create_time, prize_list_json, client_expand
      )
    values (#{seqId,jdbcType=CHAR}, #{roundId,jdbcType=BIGINT}, #{candidateId,jdbcType=BIGINT}, 
      #{uid,jdbcType=BIGINT}, #{bizId,jdbcType=INTEGER}, #{hostName,jdbcType=CHAR}, #{usedChannel,jdbcType=INTEGER}, 
      #{sid,jdbcType=BIGINT}, #{ssid,jdbcType=BIGINT}, #{totalCheerPrize,jdbcType=BIGINT}, 
      #{targetUid,jdbcType=BIGINT}, #{targetNick,jdbcType=BIGINT}, #{profitUid,jdbcType=BIGINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{prizeListJson,jdbcType=VARCHAR}, #{clientExpand,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.OmRoundRewardPrize" useGeneratedKeys="true">
    insert into om_round_reward_prize
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="seqId != null">
        seq_id,
      </if>
      <if test="roundId != null">
        round_id,
      </if>
      <if test="candidateId != null">
        candidate_id,
      </if>
      <if test="uid != null">
        uid,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="hostName != null">
        host_name,
      </if>
      <if test="usedChannel != null">
        used_channel,
      </if>
      <if test="sid != null">
        sid,
      </if>
      <if test="ssid != null">
        ssid,
      </if>
      <if test="totalCheerPrize != null">
        total_cheer_prize,
      </if>
      <if test="targetUid != null">
        target_uid,
      </if>
      <if test="targetNick != null">
        target_nick,
      </if>
      <if test="profitUid != null">
        profit_uid,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="prizeListJson != null">
        prize_list_json,
      </if>
      <if test="clientExpand != null">
        client_expand,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="seqId != null">
        #{seqId,jdbcType=CHAR},
      </if>
      <if test="roundId != null">
        #{roundId,jdbcType=BIGINT},
      </if>
      <if test="candidateId != null">
        #{candidateId,jdbcType=BIGINT},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=INTEGER},
      </if>
      <if test="hostName != null">
        #{hostName,jdbcType=CHAR},
      </if>
      <if test="usedChannel != null">
        #{usedChannel,jdbcType=INTEGER},
      </if>
      <if test="sid != null">
        #{sid,jdbcType=BIGINT},
      </if>
      <if test="ssid != null">
        #{ssid,jdbcType=BIGINT},
      </if>
      <if test="totalCheerPrize != null">
        #{totalCheerPrize,jdbcType=BIGINT},
      </if>
      <if test="targetUid != null">
        #{targetUid,jdbcType=BIGINT},
      </if>
      <if test="targetNick != null">
        #{targetNick,jdbcType=BIGINT},
      </if>
      <if test="profitUid != null">
        #{profitUid,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="prizeListJson != null">
        #{prizeListJson,jdbcType=VARCHAR},
      </if>
      <if test="clientExpand != null">
        #{clientExpand,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.hd.persist.openmode.model.OmRoundRewardPrize">
    update om_round_reward_prize
    <set>
      <if test="seqId != null">
        seq_id = #{seqId,jdbcType=CHAR},
      </if>
      <if test="roundId != null">
        round_id = #{roundId,jdbcType=BIGINT},
      </if>
      <if test="candidateId != null">
        candidate_id = #{candidateId,jdbcType=BIGINT},
      </if>
      <if test="uid != null">
        uid = #{uid,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=INTEGER},
      </if>
      <if test="hostName != null">
        host_name = #{hostName,jdbcType=CHAR},
      </if>
      <if test="usedChannel != null">
        used_channel = #{usedChannel,jdbcType=INTEGER},
      </if>
      <if test="sid != null">
        sid = #{sid,jdbcType=BIGINT},
      </if>
      <if test="ssid != null">
        ssid = #{ssid,jdbcType=BIGINT},
      </if>
      <if test="totalCheerPrize != null">
        total_cheer_prize = #{totalCheerPrize,jdbcType=BIGINT},
      </if>
      <if test="targetUid != null">
        target_uid = #{targetUid,jdbcType=BIGINT},
      </if>
      <if test="targetNick != null">
        target_nick = #{targetNick,jdbcType=BIGINT},
      </if>
      <if test="profitUid != null">
        profit_uid = #{profitUid,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="prizeListJson != null">
        prize_list_json = #{prizeListJson,jdbcType=VARCHAR},
      </if>
      <if test="clientExpand != null">
        client_expand = #{clientExpand,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.hd.persist.openmode.model.OmRoundRewardPrize">
    update om_round_reward_prize
    set seq_id = #{seqId,jdbcType=CHAR},
      round_id = #{roundId,jdbcType=BIGINT},
      candidate_id = #{candidateId,jdbcType=BIGINT},
      uid = #{uid,jdbcType=BIGINT},
      biz_id = #{bizId,jdbcType=INTEGER},
      host_name = #{hostName,jdbcType=CHAR},
      used_channel = #{usedChannel,jdbcType=INTEGER},
      sid = #{sid,jdbcType=BIGINT},
      ssid = #{ssid,jdbcType=BIGINT},
      total_cheer_prize = #{totalCheerPrize,jdbcType=BIGINT},
      target_uid = #{targetUid,jdbcType=BIGINT},
      target_nick = #{targetNick,jdbcType=BIGINT},
      profit_uid = #{profitUid,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      prize_list_json = #{prizeListJson,jdbcType=VARCHAR},
      client_expand = #{clientExpand,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.hd.persist.openmode.mapper.OmRoundCandidateMapper">
  <resultMap id="BaseResultMap" type="com.yy.hd.persist.openmode.model.OmRoundCandidate">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="round_id" jdbcType="BIGINT" property="roundId" />
    <result column="candidate_id" jdbcType="BIGINT" property="candidateId" />
    <result column="cheer_value" jdbcType="BIGINT" property="cheerValue" />
    <result column="except_reward_value" jdbcType="BIGINT" property="exceptRewardValue" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, round_id, candidate_id, cheer_value, except_reward_value, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from om_round_candidate
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from om_round_candidate
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.OmRoundCandidate" useGeneratedKeys="true">
    insert into om_round_candidate (round_id, candidate_id, cheer_value, 
      except_reward_value, create_time, update_time
      )
    values (#{roundId,jdbcType=BIGINT}, #{candidateId,jdbcType=BIGINT}, #{cheerValue,jdbcType=BIGINT}, 
      #{exceptRewardValue,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.OmRoundCandidate" useGeneratedKeys="true">
    insert into om_round_candidate
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="roundId != null">
        round_id,
      </if>
      <if test="candidateId != null">
        candidate_id,
      </if>
      <if test="cheerValue != null">
        cheer_value,
      </if>
      <if test="exceptRewardValue != null">
        except_reward_value,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="roundId != null">
        #{roundId,jdbcType=BIGINT},
      </if>
      <if test="candidateId != null">
        #{candidateId,jdbcType=BIGINT},
      </if>
      <if test="cheerValue != null">
        #{cheerValue,jdbcType=BIGINT},
      </if>
      <if test="exceptRewardValue != null">
        #{exceptRewardValue,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.hd.persist.openmode.model.OmRoundCandidate">
    update om_round_candidate
    <set>
      <if test="roundId != null">
        round_id = #{roundId,jdbcType=BIGINT},
      </if>
      <if test="candidateId != null">
        candidate_id = #{candidateId,jdbcType=BIGINT},
      </if>
      <if test="cheerValue != null">
        cheer_value = #{cheerValue,jdbcType=BIGINT},
      </if>
      <if test="exceptRewardValue != null">
        except_reward_value = #{exceptRewardValue,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.hd.persist.openmode.model.OmRoundCandidate">
    update om_round_candidate
    set round_id = #{roundId,jdbcType=BIGINT},
      candidate_id = #{candidateId,jdbcType=BIGINT},
      cheer_value = #{cheerValue,jdbcType=BIGINT},
      except_reward_value = #{exceptRewardValue,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
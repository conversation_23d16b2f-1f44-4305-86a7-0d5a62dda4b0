<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.hd.persist.openmode.mapper.StatPositionInfoMapper">
  <resultMap id="BaseResultMap" type="com.yy.hd.persist.openmode.model.StatPositionInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="position" jdbcType="INTEGER" property="position" />
    <result column="amount" jdbcType="INTEGER" property="amount" />
    <result column="award_amount" jdbcType="INTEGER" property="awardAmount" />
    <result column="award_ratio" jdbcType="VARCHAR" property="awardRatio" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, date, position, amount, award_amount, award_ratio, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stat_position_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stat_position_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.StatPositionInfo" useGeneratedKeys="true">
    insert into stat_position_info (date, position, amount, 
      award_amount, award_ratio, create_time, 
      update_time)
    values (#{date,jdbcType=DATE}, #{position,jdbcType=INTEGER}, #{amount,jdbcType=INTEGER}, 
      #{awardAmount,jdbcType=INTEGER}, #{awardRatio,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.StatPositionInfo" useGeneratedKeys="true">
    insert into stat_position_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="date != null">
        date,
      </if>
      <if test="position != null">
        position,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="awardAmount != null">
        award_amount,
      </if>
      <if test="awardRatio != null">
        award_ratio,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="position != null">
        #{position,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=INTEGER},
      </if>
      <if test="awardAmount != null">
        #{awardAmount,jdbcType=INTEGER},
      </if>
      <if test="awardRatio != null">
        #{awardRatio,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.hd.persist.openmode.model.StatPositionInfo">
    update stat_position_info
    <set>
      <if test="date != null">
        date = #{date,jdbcType=DATE},
      </if>
      <if test="position != null">
        position = #{position,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=INTEGER},
      </if>
      <if test="awardAmount != null">
        award_amount = #{awardAmount,jdbcType=INTEGER},
      </if>
      <if test="awardRatio != null">
        award_ratio = #{awardRatio,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.hd.persist.openmode.model.StatPositionInfo">
    update stat_position_info
    set date = #{date,jdbcType=DATE},
      position = #{position,jdbcType=INTEGER},
      amount = #{amount,jdbcType=INTEGER},
      award_amount = #{awardAmount,jdbcType=INTEGER},
      award_ratio = #{awardRatio,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
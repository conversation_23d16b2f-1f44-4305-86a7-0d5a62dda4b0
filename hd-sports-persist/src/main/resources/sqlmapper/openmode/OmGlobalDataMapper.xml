<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.hd.persist.openmode.mapper.OmGlobalDataMapper">
  <resultMap id="BaseResultMap" type="com.yy.hd.persist.openmode.model.OmGlobalData">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="balance" jdbcType="BIGINT" property="balance" />
    <result column="single_cheer_limit" jdbcType="BIGINT" property="singleCheerLimit" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, balance, single_cheer_limit, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from om_global_data
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from om_global_data
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.OmGlobalData" useGeneratedKeys="true">
    insert into om_global_data (balance, single_cheer_limit, update_time
      )
    values (#{balance,jdbcType=BIGINT}, #{singleCheerLimit,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.OmGlobalData" useGeneratedKeys="true">
    insert into om_global_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="balance != null">
        balance,
      </if>
      <if test="singleCheerLimit != null">
        single_cheer_limit,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="balance != null">
        #{balance,jdbcType=BIGINT},
      </if>
      <if test="singleCheerLimit != null">
        #{singleCheerLimit,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.hd.persist.openmode.model.OmGlobalData">
    update om_global_data
    <set>
      <if test="balance != null">
        balance = #{balance,jdbcType=BIGINT},
      </if>
      <if test="singleCheerLimit != null">
        single_cheer_limit = #{singleCheerLimit,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.hd.persist.openmode.model.OmGlobalData">
    update om_global_data
    set balance = #{balance,jdbcType=BIGINT},
      single_cheer_limit = #{singleCheerLimit,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
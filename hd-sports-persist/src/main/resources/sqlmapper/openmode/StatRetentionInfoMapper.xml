<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.hd.persist.openmode.mapper.StatRetentionInfoMapper">
  <resultMap id="BaseResultMap" type="com.yy.hd.persist.openmode.model.StatRetentionInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="user_type" jdbcType="VARCHAR" property="userType" />
    <result column="user_count" jdbcType="INTEGER" property="userCount" />
    <result column="retention_1" jdbcType="VARCHAR" property="retention1" />
    <result column="retention_7" jdbcType="VARCHAR" property="retention7" />
    <result column="retention_30" jdbcType="VARCHAR" property="retention30" />
    <result column="income_retention_1" jdbcType="VARCHAR" property="incomeRetention1" />
    <result column="income_retention_7" jdbcType="VARCHAR" property="incomeRetention7" />
    <result column="income_retention_30" jdbcType="VARCHAR" property="incomeRetention30" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, date, platform, user_type, user_count, retention_1, retention_7, retention_30, 
    income_retention_1, income_retention_7, income_retention_30, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stat_retention_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stat_retention_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.StatRetentionInfo" useGeneratedKeys="true">
    insert into stat_retention_info (date, platform, user_type, 
      user_count, retention_1, retention_7, 
      retention_30, income_retention_1, income_retention_7, 
      income_retention_30, create_time, update_time
      )
    values (#{date,jdbcType=DATE}, #{platform,jdbcType=VARCHAR}, #{userType,jdbcType=VARCHAR}, 
      #{userCount,jdbcType=INTEGER}, #{retention1,jdbcType=VARCHAR}, #{retention7,jdbcType=VARCHAR}, 
      #{retention30,jdbcType=VARCHAR}, #{incomeRetention1,jdbcType=VARCHAR}, #{incomeRetention7,jdbcType=VARCHAR}, 
      #{incomeRetention30,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.StatRetentionInfo" useGeneratedKeys="true">
    insert into stat_retention_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="date != null">
        date,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="userType != null">
        user_type,
      </if>
      <if test="userCount != null">
        user_count,
      </if>
      <if test="retention1 != null">
        retention_1,
      </if>
      <if test="retention7 != null">
        retention_7,
      </if>
      <if test="retention30 != null">
        retention_30,
      </if>
      <if test="incomeRetention1 != null">
        income_retention_1,
      </if>
      <if test="incomeRetention7 != null">
        income_retention_7,
      </if>
      <if test="incomeRetention30 != null">
        income_retention_30,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=VARCHAR},
      </if>
      <if test="userCount != null">
        #{userCount,jdbcType=INTEGER},
      </if>
      <if test="retention1 != null">
        #{retention1,jdbcType=VARCHAR},
      </if>
      <if test="retention7 != null">
        #{retention7,jdbcType=VARCHAR},
      </if>
      <if test="retention30 != null">
        #{retention30,jdbcType=VARCHAR},
      </if>
      <if test="incomeRetention1 != null">
        #{incomeRetention1,jdbcType=VARCHAR},
      </if>
      <if test="incomeRetention7 != null">
        #{incomeRetention7,jdbcType=VARCHAR},
      </if>
      <if test="incomeRetention30 != null">
        #{incomeRetention30,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.hd.persist.openmode.model.StatRetentionInfo">
    update stat_retention_info
    <set>
      <if test="date != null">
        date = #{date,jdbcType=DATE},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        user_type = #{userType,jdbcType=VARCHAR},
      </if>
      <if test="userCount != null">
        user_count = #{userCount,jdbcType=INTEGER},
      </if>
      <if test="retention1 != null">
        retention_1 = #{retention1,jdbcType=VARCHAR},
      </if>
      <if test="retention7 != null">
        retention_7 = #{retention7,jdbcType=VARCHAR},
      </if>
      <if test="retention30 != null">
        retention_30 = #{retention30,jdbcType=VARCHAR},
      </if>
      <if test="incomeRetention1 != null">
        income_retention_1 = #{incomeRetention1,jdbcType=VARCHAR},
      </if>
      <if test="incomeRetention7 != null">
        income_retention_7 = #{incomeRetention7,jdbcType=VARCHAR},
      </if>
      <if test="incomeRetention30 != null">
        income_retention_30 = #{incomeRetention30,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.hd.persist.openmode.model.StatRetentionInfo">
    update stat_retention_info
    set date = #{date,jdbcType=DATE},
      platform = #{platform,jdbcType=VARCHAR},
      user_type = #{userType,jdbcType=VARCHAR},
      user_count = #{userCount,jdbcType=INTEGER},
      retention_1 = #{retention1,jdbcType=VARCHAR},
      retention_7 = #{retention7,jdbcType=VARCHAR},
      retention_30 = #{retention30,jdbcType=VARCHAR},
      income_retention_1 = #{incomeRetention1,jdbcType=VARCHAR},
      income_retention_7 = #{incomeRetention7,jdbcType=VARCHAR},
      income_retention_30 = #{incomeRetention30,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
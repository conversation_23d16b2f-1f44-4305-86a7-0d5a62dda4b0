<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.hd.persist.openmode.mapper.OmPrizeMapper">
  <resultMap id="BaseResultMap" type="com.yy.hd.persist.openmode.model.OmPrize">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_id" jdbcType="INTEGER" property="bizId" />
    <result column="prize_id" jdbcType="BIGINT" property="prizeId" />
    <result column="props_id" jdbcType="BIGINT" property="propsId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="prize" jdbcType="BIGINT" property="prize" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
  </resultMap>
  <sql id="Base_Column_List">
    id, biz_id, prize_id, props_id, name, prize, icon
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from om_prize
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from om_prize
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.OmPrize" useGeneratedKeys="true">
    insert into om_prize (biz_id, prize_id, props_id, 
      name, prize, icon)
    values (#{bizId,jdbcType=INTEGER}, #{prizeId,jdbcType=BIGINT}, #{propsId,jdbcType=BIGINT}, 
      #{name,jdbcType=VARCHAR}, #{prize,jdbcType=BIGINT}, #{icon,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.yy.hd.persist.openmode.model.OmPrize" useGeneratedKeys="true">
    insert into om_prize
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="prizeId != null">
        prize_id,
      </if>
      <if test="propsId != null">
        props_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="prize != null">
        prize,
      </if>
      <if test="icon != null">
        icon,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizId != null">
        #{bizId,jdbcType=INTEGER},
      </if>
      <if test="prizeId != null">
        #{prizeId,jdbcType=BIGINT},
      </if>
      <if test="propsId != null">
        #{propsId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="prize != null">
        #{prize,jdbcType=BIGINT},
      </if>
      <if test="icon != null">
        #{icon,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.hd.persist.openmode.model.OmPrize">
    update om_prize
    <set>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=INTEGER},
      </if>
      <if test="prizeId != null">
        prize_id = #{prizeId,jdbcType=BIGINT},
      </if>
      <if test="propsId != null">
        props_id = #{propsId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="prize != null">
        prize = #{prize,jdbcType=BIGINT},
      </if>
      <if test="icon != null">
        icon = #{icon,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.hd.persist.openmode.model.OmPrize">
    update om_prize
    set biz_id = #{bizId,jdbcType=INTEGER},
      prize_id = #{prizeId,jdbcType=BIGINT},
      props_id = #{propsId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      prize = #{prize,jdbcType=BIGINT},
      icon = #{icon,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
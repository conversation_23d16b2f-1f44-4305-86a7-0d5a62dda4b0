package com.yy.hd.persist.openmode.mapper.ext;

import com.yy.hd.persist.openmode.mapper.OmRoundRewardPrizeMapper;
import com.yy.hd.persist.openmode.model.OmRoundRewardPrize;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2025/6/27 11:06
 */
public interface OmRoundRewardPrizeExtMapper extends OmRoundRewardPrizeMapper {
    @Select("select * from om_round_reward_prize where create_time >= #{minCreateTime} and uid=#{uid}")
    List<OmRoundRewardPrize> selectByMinCreateTimeAndUid(@Param("minCreateTime") Date minCreateTime, @Param("uid") long uid);

    @Select("""
            select * from om_round_reward_prize
            where round_id between #{startRoundId} and #{endRoundId} and uid = #{uid}
            """)
    List<OmRoundRewardPrize> selectByRoundIdRangeAndUid(@Param("startRoundId") long startRoundId,
                                                        @Param("endRoundId") long endRoundId,
                                                        @Param("uid") long uid);
}

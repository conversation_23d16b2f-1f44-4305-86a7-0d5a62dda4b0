package com.yy.hd.persist.openmode.mapper;

import com.yy.hd.persist.openmode.model.StatPeriodRangeInfo;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface StatPeriodRangeInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(StatPeriodRangeInfo row);

    int insertSelective(StatPeriodRangeInfo row);

    StatPeriodRangeInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(StatPeriodRangeInfo row);

    int updateByPrimaryKey(StatPeriodRangeInfo row);
}
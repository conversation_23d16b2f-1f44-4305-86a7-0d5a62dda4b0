package com.yy.hd.persist.openmode.mapper.ext;

import com.yy.hd.common.vo.LongPairVo;
import com.yy.hd.ds.DSConfig;
import com.yy.hd.ds.RwStrategy;
import com.yy.hd.persist.openmode.mapper.OmRoundMapper;
import com.yy.hd.persist.openmode.model.OmRound;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/27 11:06
 */
public interface OmRoundExtMapper extends OmRoundMapper {

    @Select("select * from om_round where round_id = #{roundId}")
    OmRound selectByRoundId(long roundId);

    @DSConfig(rwStrategy = RwStrategy.MASTER)
    @Select("select * from om_round where round_id = #{roundId}")
    OmRound loadByRoundId(long roundId);

    @Insert("""
            insert ignore into om_round(round_id, start_time, end_time, status, init_offset, config_snapshot)
            value (#{roundId},#{startTime},#{endTime},#{status},#{initOffset},#{configSnapshot})
            """)
    int insertIgnore(OmRound r);

    @Select("""
            select round_id as `key`, first_candidate_id as `value`
            from om_round where round_id between #{startRoundId} and #{endRoundId}
            """)
    List<LongPairVo> selectFirstCandidateIds(@Param("startRoundId") Long startRoundId,
                                             @Param("endRoundId") Long endRoundId);
}

package com.yy.hd.persist.openmode.mapper;

import com.yy.hd.persist.openmode.model.OmGlobalData;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface OmGlobalDataMapper {
    int deleteByPrimaryKey(Long id);

    int insert(OmGlobalData row);

    int insertSelective(OmGlobalData row);

    OmGlobalData selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OmGlobalData row);

    int updateByPrimaryKey(OmGlobalData row);
}
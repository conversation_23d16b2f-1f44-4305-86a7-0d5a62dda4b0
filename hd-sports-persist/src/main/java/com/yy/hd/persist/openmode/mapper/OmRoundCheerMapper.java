package com.yy.hd.persist.openmode.mapper;

import com.yy.hd.persist.openmode.model.OmRoundCheer;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface OmRoundCheerMapper {
    int deleteByPrimaryKey(Long id);

    int insert(OmRoundCheer row);

    int insertSelective(OmRoundCheer row);

    OmRoundCheer selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OmRoundCheer row);

    int updateByPrimaryKey(OmRoundCheer row);
}
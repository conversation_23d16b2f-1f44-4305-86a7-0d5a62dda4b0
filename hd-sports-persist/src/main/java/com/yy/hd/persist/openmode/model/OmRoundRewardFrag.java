package com.yy.hd.persist.openmode.model;

import java.util.Date;
import lombok.Data;

/**
 * om_round_reward_frag
 */
@Data
public class OmRoundRewardFrag {
    /**
     * 自增唯一ID
     */
    private Long id;

    /**
     * 送礼流水号，唯一流水号
     */
    private String seqId;

    /**
     * 轮次ID，关联 om_round.round_id
     */
    private Long roundId;

    /**
     * 助威用户uid
     */
    private Long uid;

    /**
     * 用户助威时所在频道业务ID，1-交友 2-语音房
     */
    private Integer bizId;

    /**
     * 助威时所在频道sid
     */
    private Long sid;

    /**
     * 助威时所在频道ssid
     */
    private Long ssid;

    /**
     * 奖励的碎片数量
     */
    private Long fragCount;

    /**
     * 创建时间
     */
    private Date createTime;
}
package com.yy.hd.persist.openmode.model;

import java.util.Date;
import lombok.Data;

/**
 * om_round_reward_prize
 */
@Data
public class OmRoundRewardPrize {
    /**
     * 自增唯一ID
     */
    private Long id;

    /**
     * 送礼流水号，唯一流水号
     */
    private String seqId;

    /**
     * 轮次ID，关联 om_round.round_id
     */
    private Long roundId;

    /**
     * 获胜动物ID，关联 om_candidate.id
     */
    private Long candidateId;

    /**
     * 助威用户uid
     */
    private Long uid;

    /**
     * 用户助威时所在频道业务ID，1-交友 2-语音房
     */
    private Integer bizId;

    /**
     * 来源渠道APP，dreamer-Yo交友 yomi-Yo语音 tieba-贴吧 haokan-好看 browser-PC browsermobile-web
     */
    private String hostName;

    /**
     * 营收来源渠道
     */
    private Integer usedChannel;

    /**
     * 助威时所在频道sid
     */
    private Long sid;

    /**
     * 助威时所在频道ssid
     */
    private Long ssid;

    /**
     * 用户给这个活动动物总助威值 sum(om_round_cheer.cheer_value)
     */
    private Long totalCheerPrize;

    /**
     * 收礼人uid
     */
    private Long targetUid;

    /**
     * 收礼人昵称
     */
    private Long targetNick;

    /**
     * 受益人UID
     */
    private Long profitUid;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 奖品数据，json数组格式：[{"prizeId":1,"propsId":1,"count":1}], 对应 om_prize 中的字段
     */
    private String prizeListJson;

    private String clientExpand;
}
package com.yy.hd.persist.openmode.mapper;

import com.yy.hd.persist.openmode.model.OmBuyCurrencyRecord;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface OmBuyCurrencyRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(OmBuyCurrencyRecord row);

    int insertSelective(OmBuyCurrencyRecord row);

    OmBuyCurrencyRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OmBuyCurrencyRecord row);

    int updateByPrimaryKey(OmBuyCurrencyRecord row);
}
package com.yy.hd.persist.openmode.mapper.ext;

import com.yy.hd.common.vo.LongPairVo;
import com.yy.hd.common.vo.LongRangeVo;
import com.yy.hd.persist.openmode.mapper.OmRoundCheerMapper;
import com.yy.hd.persist.openmode.model.OmRoundCheer;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2025/6/27 11:05
 */
public interface OmRoundCheerExtMapper extends OmRoundCheerMapper {

    @Select("select candidate_id as `key`, sum(fruit_count) as `value` from om_round_cheer where round_id = #{roundId} and uid = #{uid}")
    List<LongPairVo> selectCheerFruitGroupByCandidate(@Param("roundId") long roundId, @Param("uid") long uid);

    @Select("""
            select id, seq_id, round_id, candidate_id, uid, biz_id, host_name, sid, ssid, game_type, compere_uid, target_uid, profit_uid, fruit_count, cheer_value, create_time, update_time 
            from om_round_cheer 
            where round_id = #{roundId} and uid = #{uid}
            order by id desc
            """)
    List<OmRoundCheer> selectByRoundIdAndUidWithoutClientExpand(@Param("roundId") long roundId, @Param("uid") long uid);

    @Select("""
            select id, seq_id, round_id, candidate_id, uid, biz_id, host_name, sid, ssid, game_type, compere_uid, target_uid, profit_uid, fruit_count, cheer_value, create_time, update_time, client_expand 
            from om_round_cheer 
            where round_id = #{roundId} and uid = #{uid}
            order by id desc
            """)
    List<OmRoundCheer> selectByRoundIdAndUid(@Param("roundId") long roundId, @Param("uid") long uid);

    @Select("""
            select min(round_id) as `start`, max(round_id) as `end`
            from (select distinct(round_id) as round_id from om_round_cheer where create_time >= #{minCreateTime} and uid = #{uid} order by round_id desc limit #{topN})
            """)
    LongRangeVo selectRoundIdRangeByCreateTimeAndUid(@Param("minCreateTime") Date minCreateTime,
                                                     @Param("uid") long uid,
                                                     @Param("topN") long topN);

    @Select("""
            select id, seq_id, round_id, candidate_id, uid, biz_id, host_name, sid, ssid, game_type, compere_uid, target_uid, profit_uid, fruit_count, cheer_value, create_time, update_time 
            from om_round_cheer 
            where round_id between #{startRoundId} and #{endRoundId} and uid = #{uid}
            order by id desc
            """)
    List<OmRoundCheer> selectByRoundIdRangeAndUidWithoutClientExpand(@Param("startRoundId") long startRoundId,
                                                  @Param("endRoundId") long endRoundId,
                                                  @Param("uid") long uid);

}

package com.yy.hd.persist.openmode.model;

import java.util.Date;
import lombok.Data;

/**
 * om_buy_currency_record
 */
@Data
public class OmBuyCurrencyRecord {
    /**
     * 自增唯一ID
     */
    private Long id;

    /**
     * 位移流水号
     */
    private String seqId;

    /**
     * 助力用户uid
     */
    private Long uid;

    /**
     * 用户助力时所在频道业务ID，1-交友 2-语音房
     */
    private Integer bizId;

    /**
     * 来源渠道APP，dreamer-Yo交友 yomi-Yo语音 tieba-贴吧 haokan-好看 browser-PC browsermobile-web
     */
    private String hostName;

    /**
     * 营收来源渠道
     */
    private Integer usedChannel;

    /**
     * 助力时所在频道sid
     */
    private Long sid;

    /**
     * 助力时所在频道ssid
     */
    private Long ssid;

    /**
     * 被助力的uid
     */
    private Long beHelpUid;

    /**
     * 助力数量
     */
    private Long helpCount;

    /**
     * 助力金额（转换为特定业务的价值，比如交友-紫水晶，语音房-金钻）
     */
    private Long helpAmount;

    /**
     * 本次获取的果实数量
     */
    private Long fruitCount;

    /**
     * 本次助力给被助力uid累计的助力值
     */
    private Long helpValue;

    /**
     * 创建时间
     */
    private Date createTime;
}
package com.yy.hd.persist.openmode.mapper;

import com.yy.hd.persist.openmode.model.StatRetentionInfo;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface StatRetentionInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(StatRetentionInfo row);

    int insertSelective(StatRetentionInfo row);

    StatRetentionInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(StatRetentionInfo row);

    int updateByPrimaryKey(StatRetentionInfo row);
}
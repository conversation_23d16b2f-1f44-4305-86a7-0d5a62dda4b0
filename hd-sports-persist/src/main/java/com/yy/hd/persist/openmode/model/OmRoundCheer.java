package com.yy.hd.persist.openmode.model;

import java.util.Date;
import lombok.Data;

/**
 * om_round_cheer
 */
@Data
public class OmRoundCheer {
    /**
     * 自增唯一ID
     */
    private Long id;

    /**
     * 扣费流水号，唯一流水号
     */
    private String seqId;

    /**
     * 轮次ID，关联 om_round.round_id
     */
    private Long roundId;

    /**
     * 助威的动物ID，关联 om_candidate.id
     */
    private Long candidateId;

    /**
     * 助威用户uid
     */
    private Long uid;

    /**
     * 用户助威时所在频道业务ID，1-交友 2-语音房
     */
    private Integer bizId;

    /**
     * 来源渠道APP，dreamer-Yo交友 yomi-Yo语音 tieba-贴吧 haokan-好看 browser-PC browsermobile-web
     */
    private String hostName;

    /**
     * 助威时所在频道sid
     */
    private Long sid;

    /**
     * 助威时所在频道ssid
     */
    private Long ssid;

    /**
     * 当前直播间玩法类型 参考 com.yy.hd.enums.GameType
     */
    private Integer gameType;

    /**
     * 助威时候所在频道主持UID
     */
    private Long compereUid;

    /**
     * 当前选中的UID
     */
    private Long targetUid;

    /**
     * 受益人UID
     */
    private Long profitUid;

    /**
     * 本次助威果实数量
     */
    private Long fruitCount;

    /**
     * 本次助威值 fruit_count * 1000
     */
    private Long cheerValue;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    private String clientExpand;
}
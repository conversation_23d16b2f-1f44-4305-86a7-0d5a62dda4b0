package com.yy.hd.persist.openmode.model;

import java.util.Date;
import lombok.Data;

/**
 * stat_position_info
 */
@Data
public class StatPositionInfo {
    /**
     * 自增唯一ID
     */
    private Long id;

    /**
     * 统计日期
     */
    private Date date;

    /**
     * 参与位置
     */
    private Integer position;

    /**
     * 参与流水
     */
    private Integer amount;

    /**
     * 发放流水
     */
    private Integer awardAmount;

    /**
     * 发放占比=发放流水/参与流水*100%
     */
    private String awardRatio;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后一次助力值更新时间
     */
    private Date updateTime;
}
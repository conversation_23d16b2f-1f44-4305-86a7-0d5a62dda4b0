package com.yy.hd.persist.openmode.mapper;

import com.yy.hd.persist.openmode.model.OmRoundRewardPrize;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface OmRoundRewardPrizeMapper {
    int deleteByPrimaryKey(Long id);

    int insert(OmRoundRewardPrize row);

    int insertSelective(OmRoundRewardPrize row);

    OmRoundRewardPrize selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OmRoundRewardPrize row);

    int updateByPrimaryKey(OmRoundRewardPrize row);
}
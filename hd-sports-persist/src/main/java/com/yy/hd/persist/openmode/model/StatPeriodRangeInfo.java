package com.yy.hd.persist.openmode.model;

import java.util.Date;
import lombok.Data;

/**
 * stat_period_range_info
 */
@Data
public class StatPeriodRangeInfo {
    /**
     * 自增唯一ID
     */
    private Long id;

    /**
     * 统计日期
     */
    private Date date;

    /**
     * 玩法类型:超能运动会/All
     */
    private Integer arenaId;

    /**
     * 渠道类型:All/pc端/Yo交友/yo语音, web端, 好看端, 贴吧端
     */
    private String platform;

    /**
     * 参与玩法时段 一个小时一个区间
     */
    private Integer rangeId;

    /**
     * 参与流水(元)
     */
    private Integer sumIncome;

    /**
     * 成功流水(元)
     */
    private Integer winIncome;

    /**
     * 发放占比:成功流水(元)/参与流水(元)*100
     */
    private String spendingRatio;

    /**
     * 参与用户数
     */
    private Integer playUser;

    /**
     * 成功用户数
     */
    private Integer winUser;

    /**
     * 累计成功用户数
     */
    private Integer totalWinUser;

    /**
     * 累计失败用户数
     */
    private Integer totalLossUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后一次助力值更新时间
     */
    private Date updateTime;
}
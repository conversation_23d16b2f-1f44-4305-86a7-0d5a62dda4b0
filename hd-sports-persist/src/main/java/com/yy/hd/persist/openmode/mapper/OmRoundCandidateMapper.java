package com.yy.hd.persist.openmode.mapper;

import com.yy.hd.persist.openmode.model.OmRoundCandidate;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface OmRoundCandidateMapper {
    int deleteByPrimaryKey(Long id);

    int insert(OmRoundCandidate row);

    int insertSelective(OmRoundCandidate row);

    OmRoundCandidate selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OmRoundCandidate row);

    int updateByPrimaryKey(OmRoundCandidate row);
}
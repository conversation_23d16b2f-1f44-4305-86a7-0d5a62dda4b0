package com.yy.hd.persist.openmode.mapper.ext;

import com.yy.hd.persist.openmode.mapper.OmRoundCandidateMapper;
import com.yy.hd.persist.openmode.model.OmRoundCandidate;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2025/6/27 11:05
 */
public interface OmRoundCandidateExtMapper extends OmRoundCandidateMapper {

    @Select("select * from om_round_candidate where round_id = #{roundId} order by candidate_id")
    List<OmRoundCandidate> selectByRoundId(long roundId);
}

package com.yy.hd.persist.openmode.mapper.ext;

import com.yy.hd.persist.openmode.mapper.OmCandidateMapper;
import com.yy.hd.persist.openmode.model.OmCandidate;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2025/6/27 11:03
 */
public interface OmCandidateExtMapper extends OmCandidateMapper {

    @Select("select * from om_candidate order by id")
    List<OmCandidate> selectAll();
}

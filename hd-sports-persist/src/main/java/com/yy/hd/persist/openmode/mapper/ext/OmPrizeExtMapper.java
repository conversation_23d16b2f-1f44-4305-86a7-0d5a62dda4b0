package com.yy.hd.persist.openmode.mapper.ext;

import com.yy.hd.persist.openmode.mapper.OmPrizeMapper;
import com.yy.hd.persist.openmode.model.OmPrize;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2025/6/27 11:05
 */
public interface OmPrizeExtMapper extends OmPrizeMapper {

    @Select("select * from om_prize where biz_id = #{bizId}")
    List<OmPrize> selectByBizId(int bizId);
}

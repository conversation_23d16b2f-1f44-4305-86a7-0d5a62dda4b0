package com.yy.hd.persist.openmode.mapper;

import com.yy.hd.persist.openmode.model.OmRoundRewardFrag;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface OmRoundRewardFragMapper {
    int deleteByPrimaryKey(Long id);

    int insert(OmRoundRewardFrag row);

    int insertSelective(OmRoundRewardFrag row);

    OmRoundRewardFrag selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OmRoundRewardFrag row);

    int updateByPrimaryKey(OmRoundRewardFrag row);
}
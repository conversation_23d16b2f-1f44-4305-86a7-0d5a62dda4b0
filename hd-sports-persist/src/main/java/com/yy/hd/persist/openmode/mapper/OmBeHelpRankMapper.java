package com.yy.hd.persist.openmode.mapper;

import com.yy.hd.persist.openmode.model.OmBeHelpRank;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface OmBeHelpRankMapper {
    int deleteByPrimaryKey(Long id);

    int insert(OmBeHelpRank row);

    int insertSelective(OmBeHelpRank row);

    OmBeHelpRank selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OmBeHelpRank row);

    int updateByPrimaryKey(OmBeHelpRank row);
}
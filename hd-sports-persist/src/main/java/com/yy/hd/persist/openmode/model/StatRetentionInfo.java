package com.yy.hd.persist.openmode.model;

import java.util.Date;
import lombok.Data;

/**
 * stat_retention_info
 */
@Data
public class StatRetentionInfo {
    /**
     * 自增唯一ID
     */
    private Long id;

    /**
     * 统计日期
     */
    private Date date;

    /**
     * 渠道类型:All/pc端/Yo交友/yo语音, web端, 好看端, 贴吧端
     */
    private String platform;

    /**
     * 用户类型 ALL、累计成功、累计失败,新用户,老用户
     */
    private String userType;

    /**
     * 用户数
     */
    private Integer userCount;

    /**
     * 次日留存
     */
    private String retention1;

    /**
     * 7日留存
     */
    private String retention7;

    /**
     * 30日留存
     */
    private String retention30;

    /**
     * 次日参与流水留存
     */
    private String incomeRetention1;

    /**
     * 7日参与流水留存
     */
    private String incomeRetention7;

    /**
     * 30日参与流水留存
     */
    private String incomeRetention30;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后一次助力值更新时间
     */
    private Date updateTime;
}
package com.yy.hd.persist.openmode.mapper;

import com.yy.hd.persist.openmode.model.OmRound;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface OmRoundMapper {
    int deleteByPrimaryKey(Long id);

    int insert(OmRound row);

    int insertSelective(OmRound row);

    OmRound selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OmRound row);

    int updateByPrimaryKeyWithBLOBs(OmRound row);

    int updateByPrimaryKey(OmRound row);
}
package com.yy.hd.persist.openmode.mapper;

import com.yy.hd.persist.openmode.model.OmPrize;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface OmPrizeMapper {
    int deleteByPrimaryKey(Long id);

    int insert(OmPrize row);

    int insertSelective(OmPrize row);

    OmPrize selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OmPrize row);

    int updateByPrimaryKey(OmPrize row);
}
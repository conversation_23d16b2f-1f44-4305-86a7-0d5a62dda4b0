package com.yy.hd.persist.openmode.mapper;

import com.yy.hd.persist.openmode.model.StatTopUserInfo;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface StatTopUserInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(StatTopUserInfo row);

    int insertSelective(StatTopUserInfo row);

    StatTopUserInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(StatTopUserInfo row);

    int updateByPrimaryKey(StatTopUserInfo row);
}
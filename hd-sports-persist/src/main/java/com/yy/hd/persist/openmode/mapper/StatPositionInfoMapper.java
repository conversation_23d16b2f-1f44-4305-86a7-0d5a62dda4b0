package com.yy.hd.persist.openmode.mapper;

import com.yy.hd.persist.openmode.model.StatPositionInfo;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface StatPositionInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(StatPositionInfo row);

    int insertSelective(StatPositionInfo row);

    StatPositionInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(StatPositionInfo row);

    int updateByPrimaryKey(StatPositionInfo row);
}
package com.yy.hd.persist.openmode.mapper;

import com.yy.hd.persist.openmode.model.StatPaidRangeInfo;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface StatPaidRangeInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(StatPaidRangeInfo row);

    int insertSelective(StatPaidRangeInfo row);

    StatPaidRangeInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(StatPaidRangeInfo row);

    int updateByPrimaryKey(StatPaidRangeInfo row);
}
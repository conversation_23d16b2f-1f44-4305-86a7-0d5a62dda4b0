package com.yy.hd.persist.openmode.model;

import java.util.Date;
import lombok.Data;

/**
 * om_round_candidate
 */
@Data
public class OmRoundCandidate {
    /**
     * 自增唯一ID
     */
    private Long id;

    /**
     * 轮次ID，关联 om_round.round_id
     */
    private Long roundId;

    /**
     * 候选动物ID，关联 om_candidate.id
     */
    private Long candidateId;

    /**
     * 助威总额(助威果实数 * 1000)
     */
    private Long cheerValue;

    /**
     * 期望发奖额（助威的果实数 * 1000 * om_candidate.reward_rate）
     */
    private Long exceptRewardValue;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;
}
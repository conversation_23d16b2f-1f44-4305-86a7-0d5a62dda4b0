package com.yy.hd.persist.openmode.mapper;

import com.yy.hd.persist.openmode.model.OmCandidate;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface OmCandidateMapper {
    int deleteByPrimaryKey(Long id);

    int insert(OmCandidate row);

    int insertSelective(OmCandidate row);

    OmCandidate selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OmCandidate row);

    int updateByPrimaryKey(OmCandidate row);
}
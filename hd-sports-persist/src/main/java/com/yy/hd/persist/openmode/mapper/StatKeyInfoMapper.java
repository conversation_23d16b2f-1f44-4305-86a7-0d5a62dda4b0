package com.yy.hd.persist.openmode.mapper;

import com.yy.hd.persist.openmode.model.StatKeyInfo;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface StatKeyInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(StatKeyInfo row);

    int insertSelective(StatKeyInfo row);

    StatKeyInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(StatKeyInfo row);

    int updateByPrimaryKey(StatKeyInfo row);
}
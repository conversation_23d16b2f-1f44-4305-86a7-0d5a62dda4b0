<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd" >
<generatorConfiguration>

    <context id="entmobile" defaultModelType="flat" targetRuntime="MyBatis3">

        <plugin type="org.mybatis.generator.plugins.MapperAnnotationPlugin"/>
        <!-- lombok code        -->
        <plugin type="org.mybatis.generator.plugins.LombokPlugin"/>

        <commentGenerator type="org.mybatis.generator.comments.MobyyCommentGenerator"/>

        <!-- //////////////////////////////////////////////////////////////////////////////////////////////////////////////// -->
        <!-- jdbc connection -->
        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="${jdbcUrl}"
                        userId="${user}"
                        password="${password}"/>

        <!-- //////////////////////////////////////////////////////////////////////////////////////////////////////////////// -->
        <!-- java type resolver -->
        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <!-- //////////////////////////////////////////////////////////////////////////////////////////////////////////////// -->
        <!-- gem entity -->
        <!--data -->
        <javaModelGenerator targetPackage="${modelPackage}"
                            targetProject="${modelSrcDir}">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="false"/>
        </javaModelGenerator>

        <!-- //////////////////////////////////////////////////////////////////////////////////////////////////////////////// -->
        <!-- gem annotated mapper -->
        <sqlMapGenerator targetPackage="sqlmapper/openmode" targetProject="./src/main/resources">
            <property name="enableSubPackages" value="false"/>
            <property name="overwrite" value="true"/> <!-- 添加覆盖属性 -->
        </sqlMapGenerator>
        <!-- data XMLMAPPER ANNOTATEDMAPPER -->
        <javaClientGenerator type="XMLMAPPER"
                             targetPackage="${mapperPackage}"
                             targetProject="${mapperSrcDir}">
            <property name="enableSubPackages" value="true"/>
            <property name="overwrite" value="true"/> <!-- 添加覆盖属性 -->
        </javaClientGenerator>

        <table tableName="${tableName}" domainObjectName="${domainObjectName}"
               enableDeleteByPrimaryKey="${enableDeleteByPrimaryKey}"
               enableInsert="${enableInsert}"
               enableUpdateByPrimaryKey="${enableUpdateByPrimaryKey}"
               enableSelectByPrimaryKey="${enableSelectByPrimaryKey}"
               enableCountByExample="${enableCountByExample}"
               enableDeleteByExample="${enableDeleteByExample}"
               enableSelectByExample="${enableSelectByExample}"
               enableUpdateByExample="${enableUpdateByExample}">
            <generatedKey column="id" sqlStatement="JDBC"/>
        </table>
    </context>
</generatorConfiguration>

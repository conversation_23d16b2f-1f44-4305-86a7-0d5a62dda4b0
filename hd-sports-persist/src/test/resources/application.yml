spring:
  application:
    name: ${MY_PROJECT_NAME:hd-sports-app}
  data:
    redis: # https://s.sysop.yy.com/commonComps/dbms/manage/27900/instances/overview ./redis-cli -h 10.12.36.93 -p 4025
      database: 6
      connect-timeout: 1000
      timeout: 1000
      sentinel:
        master: hudong_redis_test_001
        nodes:
          - fstst-group0001-001-sentinel.yy.com:20058
          - fstst-group0001-002-sentinel.yy.com:20058
          - fstst-group0001-003-sentinel.yy.com:20058
      lettuce:
        pool:
          max-active: 30
          max-idle: 10
          min-idle: 5
          time-between-eviction-runs: 3000ms
          max-wait: 1000
  datasource:
    hikari: # https://s.sysop.yy.com/commonComps/dbms/manage/27901/instances/overview
      master: # 读写一致
        jdbc-url: ***************************************************************************************************************************************
        username: hudong_test@hudong_mysql_test
        password: 2cJKDaxSsaUaYAuqcoEd1VP4
        minimum-idle: 3
        maximum-pool-size: 10
        connection-timeout: 1000
        connection-test-query: select 1
        idle-timeout: 600000
        max-lifetime: 1800000
        driver-class-name: com.mysql.cj.jdbc.Driver
      sep: # 读写分离
        jdbc-url: ***************************************************************************************************************************************
        username: hudong_test@hudong_mysql_test
        password: 2cJKDaxSsaUaYAuqcoEd1VP4
        minimum-idle: 3
        maximum-pool-size: 10
        connection-timeout: 1000
        connection-test-query: select 1
        idle-timeout: 600000
        max-lifetime: 1800000
        driver-class-name: com.mysql.cj.jdbc.Driver

mybatis:
  mapper-locations: classpath*:sqlmapper/**/*.xml
  configuration:
    map-underscore-to-camel-case: true
logging:
  config: classpath:log4j2-spring.xml
package org.mybatis.generator.plugins;

import org.mybatis.generator.api.IntrospectedColumn;
import org.mybatis.generator.api.IntrospectedTable;
import org.mybatis.generator.api.PluginAdapter;
import org.mybatis.generator.api.dom.java.*;

import java.util.List;

public class LombokPlugin extends PluginAdapter {


    private static void addGeneratedAnnotation(Interface unit, IntrospectedTable table) {
//        unit.addFileCommentLine("/*");
//        unit.addFileCommentLine(" * Autogenerated based on "+ table.getTableConfiguration().getTableName() +" by My<PERSON><PERSON> Plugin.");
//        unit.addFileCommentLine(" * ");
//        unit.addFileCommentLine(" * DO NOT EDIT UNLESS YOU ARE A GREEN HAND.");
//        unit.addFileCommentLine(" */");
    }

    private static void addGeneratedAnnotation(TopLevelClass unit, IntrospectedTable table,int model) {
        if (model > 0) {
//            unit.addImportedType(new FullyQualifiedJavaType("lombok.Getter"));
//            unit.addImportedType(new FullyQualifiedJavaType("lombok.Setter"));
//            unit.addImportedType(new FullyQualifiedJavaType("lombok.experimental.Accessors"));
//            unit.addAnnotation("@Getter");
//            unit.addAnnotation("@Setter");
//            if (model == 1) {
//                unit.addImportedType(new FullyQualifiedJavaType("lombok.ToString"));
//                unit.addAnnotation("@ToString");
//            }
//            unit.addAnnotation("@Accessors(chain = true)");
            unit.addImportedType(new FullyQualifiedJavaType("lombok.Data"));
            unit.addAnnotation("@Data");
        }
//        unit.addFileCommentLine("/**");
//        unit.addFileCommentLine(" * Autogenerated based on "+ table.getTableConfiguration().getTableName() +" by Mybatis Plugin.");
//        unit.addFileCommentLine(" * ");
//        unit.addFileCommentLine(" * DO NOT EDIT UNLESS YOU ARE A GREEN HAND.");
//        unit.addFileCommentLine(" */");
    }

    @Override
    public boolean validate(List<String> warnings) {
        return true;
    }

    @Override
    public boolean modelExampleClassGenerated(TopLevelClass topLevelClass, IntrospectedTable introspectedTable) {

        Method instance = new Method("instance");
        instance.setReturnType(topLevelClass.getType());
        instance.setStatic(true);
        instance.setVisibility(JavaVisibility.PUBLIC);
        instance.addBodyLine(String.format("return new %s();", topLevelClass.getType().getShortNameWithoutTypeArguments()));
        topLevelClass.addMethod(instance);

        
        topLevelClass.getMethods().stream().filter(
                method -> "setOrderByClause".equals(method.getName())
        ).findAny().ifPresent(
                method ->
                {
                    method.setReturnType(topLevelClass.getType());
                    method.addBodyLine("return this;");
                }
        );

        topLevelClass.getInnerClasses().stream().filter(innerClass -> "Criteria".equals(
                innerClass.getType().getFullyQualifiedName()
        )).findAny().ifPresent(
                innerClass -> {
                    Field field = new Field("example", topLevelClass.getType());
                    field.setVisibility(JavaVisibility.PRIVATE);
                    innerClass.addField(field);
                    Method method = new Method("example");
                    method.setReturnType(topLevelClass.getType());
                    method.setVisibility(JavaVisibility.PUBLIC);
                    method.addBodyLine("return this.example;");
                    innerClass.addMethod(method);
                }
        );

        topLevelClass.getMethods().stream().filter(
                method -> "createCriteria".equals(method.getName())
        ).findAny().ifPresent(
                method -> method.addBodyLine(1, "criteria.example = this;")
        );

        addGeneratedAnnotation(topLevelClass, introspectedTable,2);

        paging(topLevelClass);

        limitOffset(topLevelClass);

        return true;
    }

    @Override
    public boolean providerSelectByExampleWithBLOBsMethodGenerated(Method method, TopLevelClass topLevelClass, IntrospectedTable introspectedTable) {
        addPaging(method);
        return true;
    }

    @Override
    public boolean providerSelectByExampleWithoutBLOBsMethodGenerated(Method method, TopLevelClass topLevelClass, IntrospectedTable introspectedTable) {
        addPaging(method);
        return true;
    }

    private void addPaging(Method method) {
        int index = method.getBodyLines().size() - 1;
        method.addBodyLine(index++, "if (example != null && example.getLimit() != null) {");
        method.addBodyLine(index++, "sql.LIMIT(example.getLimit());");
        method.addBodyLine(index++, "}");
        method.addBodyLine(index++, "if (example != null && example.getOffset() != null) {");
        method.addBodyLine(index++, "sql.OFFSET(example.getOffset());");
        method.addBodyLine(index++, "}");
        method.addBodyLine(index++, "if (example != null && example.getPageNum() != null && example.getPageSize() != null) {");
        method.addBodyLine(index++, "sql.LIMIT(example.getPageSize());");
        method.addBodyLine(index++, "sql.OFFSET((example.getPageNum() - 1) * example.getPageSize());");
        method.addBodyLine(index, "}");
    }

    private void limitOffset(TopLevelClass topLevelClass) {
        PrimitiveTypeWrapper integerWrapper = FullyQualifiedJavaType.getIntInstance().getPrimitiveTypeWrapper();

        Field limit = new Field("limit", integerWrapper);
        limit.setVisibility(JavaVisibility.PRIVATE);
        topLevelClass.addField(limit);

        Field offset = new Field("offset", integerWrapper);
        offset.setVisibility(JavaVisibility.PRIVATE);
        topLevelClass.addField(offset);

    }

    private void paging(TopLevelClass topLevelClass) {
        PrimitiveTypeWrapper integerWrapper = FullyQualifiedJavaType.getIntInstance().getPrimitiveTypeWrapper();

        Field pageNum = new Field("pageNum", integerWrapper);
        pageNum.setVisibility(JavaVisibility.PRIVATE);
        topLevelClass.addField(pageNum);

        Field pageSize = new Field("pageSize", integerWrapper);
        pageSize.setVisibility(JavaVisibility.PRIVATE);
        topLevelClass.addField(pageSize);

    }

    @Override
    public boolean providerGenerated(TopLevelClass topLevelClass, IntrospectedTable introspectedTable) {
        addGeneratedAnnotation(topLevelClass, introspectedTable,0);
        return true;
    }

    @Override
    public boolean clientGenerated(Interface interfaze, IntrospectedTable introspectedTable) {
        addGeneratedAnnotation(interfaze, introspectedTable);
        return true;
    }

    @Override
    public boolean modelBaseRecordClassGenerated(TopLevelClass topLevelClass, IntrospectedTable introspectedTable) {
        addGeneratedAnnotation(topLevelClass, introspectedTable,1);
        return true;
    }

    @Override
    public boolean modelSetterMethodGenerated(Method method, TopLevelClass topLevelClass, IntrospectedColumn introspectedColumn, IntrospectedTable introspectedTable, ModelClassType modelClassType) {
        return false;
    }

    @Override
    public boolean modelGetterMethodGenerated(Method method, TopLevelClass topLevelClass, IntrospectedColumn introspectedColumn, IntrospectedTable introspectedTable, ModelClassType modelClassType) {
        return false;
    }
}

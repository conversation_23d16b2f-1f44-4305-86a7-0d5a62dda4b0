package org.mybatis.generator;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/1/26 14:04
 */
@Data
public class GenConfig {

    /** db */
    private String jdbcUrl;
    private String user;
    private String password;

    /** model */
    private String modelPackage;
    private String modelSrcDir;

    /** mapper */
    private String mapperPackage;
    private String mapperSrcDir;

    private String tableName;
    private String domainObjectName;
    private boolean enableDeleteByPrimaryKey = true;
    private boolean enableInsert = true;
    private boolean enableUpdateByPrimaryKey = true;
    private boolean enableSelectByPrimaryKey = true;
    private boolean enableCountByExample = false;
    private boolean enableDeleteByExample = false;
    private boolean enableSelectByExample = false;
    private boolean enableUpdateByExample = false;

    public GenConfig withDB(String jdbcUrl) {
        this.jdbcUrl = jdbcUrl;
        return this;
    }

    public GenConfig withModel(String pkg, String srcDir) {
        this.modelPackage = pkg;
        this.modelSrcDir = srcDir;
        return this;
    }

    public GenConfig withMapper(String pkg, String srcDir) {
        this.mapperPackage = pkg;
        this.mapperSrcDir = srcDir;
        return this;
    }

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>() {
            {
                put("jdbcUrl", jdbcUrl);
                put("user", user);
                put("password", password);
                put("modelPackage", modelPackage);
                put("modelSrcDir", modelSrcDir);
                put("mapperPackage", mapperPackage);
                put("mapperSrcDir", mapperSrcDir);
                put("tableName", tableName);
                put("domainObjectName", domainObjectName);
                put("enableDeleteByPrimaryKey", enableDeleteByPrimaryKey);
                put("enableInsert", enableInsert);
                put("enableUpdateByPrimaryKey", enableUpdateByPrimaryKey);
                put("enableSelectByPrimaryKey", enableSelectByPrimaryKey);
                put("enableCountByExample", enableCountByExample);
                put("enableDeleteByExample", enableDeleteByExample);
                put("enableSelectByExample", enableSelectByExample);
                put("enableUpdateByExample", enableUpdateByExample);
            }
        };
        return map;
    }
}

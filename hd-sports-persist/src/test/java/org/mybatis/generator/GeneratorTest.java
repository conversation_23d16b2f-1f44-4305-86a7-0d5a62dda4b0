package org.mybatis.generator;

import com.yy.hd.util.PlaceholderUtil;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mybatis.generator.api.MyBatisGenerator;
import org.mybatis.generator.api.VerboseProgressCallback;
import org.mybatis.generator.config.Configuration;
import org.mybatis.generator.config.xml.ConfigurationParser;
import org.mybatis.generator.internal.DefaultShellCallback;
import org.springframework.core.io.InputStreamResource;

import javax.xml.parsers.DocumentBuilderFactory;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.Reader;
import java.io.StringReader;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * mybatis代码生成插件调用者.
 */
public class GeneratorTest {

    protected static final String PROJECT_PREFIX = "hd-sports-";
    /**
     * 项目根路径
     */
    protected static String projectRoot;

    static {
        System.setProperty(DocumentBuilderFactory.class.getName(),
                "com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderFactoryImpl");
    }

    public static void generate(String xmlPath, GenConfig cfg) throws Exception {
        List<String> warnings = new ArrayList<>();
        ConfigurationParser cp = new ConfigurationParser(warnings);

        String content = IOUtils.toString(Objects.requireNonNull(
                        GeneratorTest.class.getClassLoader().getResourceAsStream(xmlPath)),
                StandardCharsets.UTF_8);

        String jdbcUrl = cfg.getJdbcUrl();
        if (!jdbcUrl.contains("&amp;")) {
            jdbcUrl = jdbcUrl.replace("&", "&amp;");
        }
        cfg.setJdbcUrl(jdbcUrl);

        Map<String, Object> params = cfg.toMap();
        String xml = PlaceholderUtil.resolvePlaceholder(content, params);

        Reader reader = new StringReader(xml);
        Configuration config = cp.parseConfiguration(reader);

        MyBatisGenerator myBatisGenerator = new MyBatisGenerator(config, new DefaultShellCallback(true), warnings);
        System.out.println("------- generator begin -------");
        File f = new File("././src/main/java");
        f.mkdirs();
        myBatisGenerator.generate(new VerboseProgressCallback());
        for (String warning : warnings) {
            System.out.println(warning);
        }
        System.out.println("------- generator end -------");
    }

    @BeforeEach
    public void ready() {
        String current = Objects.requireNonNull(GeneratorTest.class.getResource("")).getPath();
        projectRoot = current.substring(0, current.indexOf(PROJECT_PREFIX));
        System.out.println("projectRoot: " + projectRoot);
    }

    private InputStreamResource newStringInputResource(String content) {
        return new InputStreamResource(new ByteArrayInputStream(content.getBytes()));
    }

    private GenConfig getGenConfig(String modelPkg, String mapperPkg) {
//        ConfigFile apolloCfg = ConfigService.getConfigFile("application", ConfigFileFormat.YML);
//        YamlPropertiesFactoryBean factoryBean = new YamlPropertiesFactoryBean();
//        factoryBean.setResources(newStringInputResource(apolloCfg.getContent()));
//        factoryBean.afterPropertiesSet();
//        Properties config = factoryBean.getObject();

        Properties config = System.getProperties();

        GenConfig cfg = new GenConfig();
        cfg.setJdbcUrl(config.getProperty("spring.datasource.url", ""));
        cfg.setUser(config.getProperty("spring.datasource.username", ""));
        cfg.setPassword(config.getProperty("spring.datasource.password", ""));
        cfg.setModelSrcDir("./src/main/java");
        cfg.setMapperSrcDir("./src/main/java");
        cfg.setModelPackage(modelPkg);
        cfg.setMapperPackage(mapperPkg);

        return cfg;
    }

    private void clearOldMapperXml(String domainObjectName) {
        // 清理旧的 xxxMapper.xml
        String oldMapperXml = String.format("%s/%spersist/src/main/resources/sqlmapper/openmode/%sMapper.xml",
                projectRoot, PROJECT_PREFIX, domainObjectName);

        File file = new File(oldMapperXml);
        if (file.exists()) {
            System.out.println("delete old mapper xml: " + oldMapperXml);
            file.delete();
        } else {
            System.out.println("old mapper xml not exists: " + oldMapperXml);
        }
    }

    private void generate(String table, String domainObjectName) throws Exception {
        GenConfig cfg = getGenConfig(
                "com.yy.hd.persist.openmode.model",
                "com.yy.hd.persist.openmode.mapper"
        );
        cfg.setTableName(table);
        cfg.setDomainObjectName(domainObjectName);
        clearOldMapperXml(cfg.getDomainObjectName());
        generate("generatorConfigTpl.xml", cfg);
    }

    @Test
    public void generateMyBatisCode() throws Exception {
//        generate("om_global_data", "OmGlobalData");
//        generate("om_round", "OmRound");
//         generate("om_candidate", "OmCandidate");
//        generate("om_prize", "OmPrize");
//        generate("om_round_candidate", "OmRoundCandidate");
        generate("om_round_cheer", "OmRoundCheer");
//        generate("om_round_reward_prize", "OmRoundRewardPrize");
//        generate("om_round_reward_frag", "OmRoundRewardFrag");
//        generate("om_buy_currency_record", "OmBuyCurrencyRecord");
//        generate("om_be_help_rank", "OmBeHelpRank");

        // 统计相关表
//        generate("stat_key_info", "StatKeyInfo");
//        generate("stat_paid_range_info", "StatPaidRangeInfo");
//        generate("stat_period_range_info", "StatPeriodRangeInfo");
//        generate("stat_position_info", "StatPositionInfo");
//        generate("stat_retention_info", "StatRetentionInfo");
//        generate("stat_top_user_info", "StatTopUserInfo");
    }
}

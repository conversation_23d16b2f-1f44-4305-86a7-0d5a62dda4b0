package com.yy.hd.persist;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 *
 * <AUTHOR>
 * @since 2025/6/26 18:52
 */
@SpringBootTest(classes = {RedisAutoConfiguration.class})
@Slf4j
public class RedisTemplateTest {

    @Autowired
    StringRedisTemplate redisTemplate;

    @Test
    void testInject() {
        Assertions.assertNotNull(redisTemplate);
        var v = redisTemplate.opsForValue().get("test");
        System.out.println(v);
    }
}

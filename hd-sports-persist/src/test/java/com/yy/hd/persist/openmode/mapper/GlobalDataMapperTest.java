package com.yy.hd.persist.openmode.mapper;

import com.yy.hd.config.DataSourceConfig;
import com.yy.hd.persist.openmode.mapper.ext.OmGlobalDataExtMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025/6/26 18:27
 */
@SpringBootTest(classes = {DataSourceConfig.class})
@Slf4j
class GlobalDataMapperTest {

    @Resource
    OmGlobalDataMapper omGlobalDataMapper;

    @Resource
    OmGlobalDataExtMapper omGlobalDataExtMapper;

    @Test
    void selectByPrimaryKey() {
        Assertions.assertNotNull(omGlobalDataMapper);
        var obj = omGlobalDataMapper.selectByPrimaryKey(-1L);
        Assertions.assertNull(obj);

        Assertions.assertNotNull(omGlobalDataExtMapper);
        obj = omGlobalDataExtMapper.selectByPrimaryKey(-1L);
        Assertions.assertNull(obj);
    }
}
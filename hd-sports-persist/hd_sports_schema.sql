-- 开奖模式 系统全局数据信息
CREATE TABLE IF NOT EXISTS `hd_sports`.`om_global_data`
(
    `id`                 bigint(20) AUTO_INCREMENT COMMENT '自增唯一ID',
    `balance`            bigint(20) NOT NULL COMMENT '余额，用户助威时候 incr，出奖的时候 decr',
    `single_cheer_limit` bigint(20) NOT NULL COMMENT '单次助威金钻上限（金钻|紫水晶数）',
    `update_time`        datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT '系统全局数据信息';

-- 轮次信息（40秒一轮，一年 788400，10年<1千万）
-- round_id 怎么生成？这个先做成一个接口吧，后面方便替换
CREATE TABLE IF NOT EXISTS `hd_sports`.`om_round`
(
    `id`                  bigint(20) AUTO_INCREMENT COMMENT '自增唯一ID',
    `round_id`            bigint(20)   NOT NULL COMMENT '轮次ID, 唯一',
    `start_time`          datetime     NOT NULL COMMENT '开始时间',
    `end_time`            datetime     NOT NULL COMMENT '结束时间',
    `status`              int(11)      NOT NULL DEFAULT 1 COMMENT '状态 1：参与期（冲刺前）-允许助威 2：锁定期（开始冲刺-不允许助威） 3：公布结果期（决出冠军）',
    `init_offset`         varchar(256) NOT NULL COMMENT '初始比赛偏移值 candidateId1:offset1,candidateId2:offset2.... 其中 candidateId 关联om_candidate.id',
    `first_candidate_id`  bigint(20) COMMENT '最终胜出，第一名的动物ID 关联 om_candidate.id',
    `other_candidate_ids` varchar(1024) COMMENT '非第一的其他动物ID排行，英文逗号分隔多个动物id candidateId1,candidateId2... 其中 candidateId 关联om_candidate.id',
    `champion_uid`        bigint(20) COMMENT '助力冠军uid，关联 om_round_reward_prize.uid',
    `create_time`         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `config_snapshot`     text COMMENT '配置快照信息，用于结算，记录每个动物的概率: candidateId1:probability1,candidateId2:probability2.... 其中 candidateId 关联om_candidate.id',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_round_id` (`round_id`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT '轮次信息 (服务治理添加按create_time 周期性清理)';

-- 参赛候选信息表 如参赛动物
CREATE TABLE IF NOT EXISTS `hd_sports`.`om_candidate`
(
    `id`          bigint(20) AUTO_INCREMENT COMMENT '自增唯一ID',
    `name`        varchar(64)  NOT NULL COMMENT '动物名称',
    `award_desc`  varchar(128) NOT NULL COMMENT '说明',
    `icon`        varchar(256) NOT NULL COMMENT '动物图标',
    `reward_rate` int(11)      NOT NULL COMMENT '助威成功比例，结算时候用户 助威金额 * 这个比例 来计算发奖',
    `probability` bigint(20)   NOT NULL COMMENT '获胜权重,用于计算概率',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT '参赛候选信息表 如参赛动物';

-- 初始化参赛候选动物表
INSERT IGNORE INTO `hd_sports`.`om_candidate` (id, name, award_desc, icon, reward_rate, probability)
VALUES (1, '迅影猴', '微量礼物组合', 'https://hd-static.yystatic.com/2506496411344654.png', 2, 4825000),
       (2, '滚石猫', '少量礼物组合', 'https://hd-static.yystatic.com/4818427817680142.png', 5, 1900000),
       (3, '流星兔', '少量礼物组合', 'https://hd-static.yystatic.com/9404122138492332.png', 5, 1900000),
       (4, '奔雷虎', '中等礼物组合', 'https://hd-static.yystatic.com/7528522606556645.png', 10, 925000),
       (5, '闪电鼠', '大量礼物组合', 'https://hd-static.yystatic.com/18932783707905698.png', 25, 330000),
       (6, '火箭龟', '超级礼物组合', 'https://hd-static.yystatic.com/6501244442228789.png', 50, 120000)
;

-- 获胜获取奖品信息表
CREATE TABLE IF NOT EXISTS `hd_sports`.`om_prize`
(
    `id`       bigint(20) AUTO_INCREMENT COMMENT '自增唯一ID',
    `biz_id`   int(11)      NOT NULL COMMENT '业务ID，1-交友 2-语音房',
    `prize_id` bigint(20)   NOT NULL COMMENT '业务奖励ID',
    `props_id` bigint(20)   NOT NULL COMMENT '营收礼物ID',
    `name`     varchar(32)  NOT NULL COMMENT '礼物名称',
    `prize`    bigint(20)   NOT NULL COMMENT '礼物价值',
    `icon`     varchar(256) NOT NULL COMMENT '礼物图标',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT '获胜获取奖品信息表';

-- 初始化奖品数据
INSERT IGNORE INTO `hd_sports`.`om_prize` (id, biz_id, prize_id, props_id, name, prize, icon)
VALUES (1, 1, 1004, 20557, '宠物Q蛋', 1000, 'https://zhuiya.bs2cdn.yy.com/test/adminweb/sports/1.png'),
       (2, 1, 1006, 20558, '抱抱Q宠', 10000, 'https://zhuiya.bs2cdn.yy.com/test/adminweb/sports/2.png'),
       (3, 1, 1005, 20559, 'Q宠初长成', 100000, 'https://zhuiya.bs2cdn.yy.com/test/adminweb/sports/3.png'),
       (4, 1, 1001, 20560, '神宠出世', 1000000, 'https://zhuiya.bs2cdn.yy.com/test/adminweb/sports/4.png'),
       (5, 2, 1004, 340361, '宠物Q蛋', 1000, 'https://zhuiya.bs2cdn.yy.com/test/adminweb/sports/1.png'),
       (6, 2, 1006, 340362, '抱抱Q宠', 10000, 'https://zhuiya.bs2cdn.yy.com/test/adminweb/sports/2.png'),
       (7, 2, 1005, 340363, 'Q宠初长成', 100000, 'https://zhuiya.bs2cdn.yy.com/test/adminweb/sports/3.png'),
       (8, 2, 1001, 340364, '神宠出世', 1000000, 'https://zhuiya.bs2cdn.yy.com/test/adminweb/sports/4.png')
;

-- 本轮每个候选者助威的统计数据
CREATE TABLE IF NOT EXISTS `hd_sports`.`om_round_candidate`
(
    `id`                  bigint(20) AUTO_INCREMENT COMMENT '自增唯一ID',
    `round_id`            bigint(20) NOT NULL COMMENT '轮次ID，关联 om_round.round_id',
    `candidate_id`        bigint(20) NOT NULL COMMENT '候选动物ID，关联 om_candidate.id',
    `cheer_value`         bigint(20) NOT NULL DEFAULT 0 COMMENT '助威总额(助威果实数 * 1000)',
    `except_reward_value` bigint(20) NOT NULL DEFAULT 0 COMMENT '期望发奖额（助威的果实数 * 1000 * om_candidate.reward_rate）',
    `create_time`         datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`         datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT '本轮每个候选者助威的统计数据 (服务治理添加按create_time 周期性清理)';

-- 用户本轮助威明细记录(助威的时候写入)
-- 助威时候，1. 累计一个全局助威总数值 2. 累加当前助威动物的统计数值 (om_round_candidate.cheer_value, except_award_value)
CREATE TABLE IF NOT EXISTS `hd_sports`.`om_round_cheer`
(
    `id`            bigint(20) AUTO_INCREMENT COMMENT '自增唯一ID',
    `seq_id`        char(64)      NOT NULL COMMENT '扣费流水号，唯一流水号',
    `round_id`      bigint(20)    NOT NULL COMMENT '轮次ID，关联 om_round.round_id',
    `candidate_id`  bigint(20)    NOT NULL COMMENT '助威的动物ID，关联 om_candidate.id',
    `uid`           bigint(20)    NOT NULL COMMENT '助威用户uid',
    `biz_id`        int(11)       NOT NULL COMMENT '用户助威时所在频道业务ID，1-交友 2-语音房',
    `host_name`     char(32)      NOT NULL COMMENT '来源渠道APP，dreamer-Yo交友 yomi-Yo语音 tieba-贴吧 haokan-好看 browser-PC browsermobile-web',
    `sid`           bigint(20)    NOT NULL COMMENT '助威时所在频道sid',
    `ssid`          bigint(20)    NOT NULL COMMENT '助威时所在频道ssid',
    `game_type`     int(11)       NOT NULL DEFAULT 0 COMMENT '当前直播间玩法类型 参考 com.yy.hd.enums.GameType',
    `compere_uid`   bigint(20)    NOT NULL COMMENT '助威时候所在频道主持UID',
    `target_uid`    bigint(20)    NOT NULL COMMENT '当前选中的UID',
    `profit_uid`    bigint(20)    NOT NULL COMMENT '受益人UID',
    `fruit_count`   bigint(20)    NOT NULL COMMENT '本次助威果实数量',
    `cheer_value`   bigint(20)    NOT NULL DEFAULT 0 COMMENT '本次助威值 fruit_count * 1000',
    `create_time`   datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    `client_expand` varchar(2048) NOT NULL DEFAULT '客户端扩展参数，开奖的时候需要用来去营收送礼物，必须的',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_seq_id` (`seq_id`),
    INDEX `idx_round_candidate_uid_biz` (`round_id`, `candidate_id`, `uid`, `biz_id`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT '用户本轮助威明细记录 (服务治理添加按create_time 周期性清理)';

-- 用户本轮助威成功奖励列表记录
-- 结算的时候，如果助威成功，将会发放特定一个或者多个礼物
CREATE TABLE IF NOT EXISTS `hd_sports`.`om_round_reward_prize`
(
    `id`                bigint(20) AUTO_INCREMENT COMMENT '自增唯一ID',
    `seq_id`            char(64)      NOT NULL COMMENT '送礼流水号，唯一流水号',
    `round_id`          bigint(20)    NOT NULL COMMENT '轮次ID，关联 om_round.round_id',
    `candidate_id`      bigint(20)    NOT NULL COMMENT '获胜动物ID，关联 om_candidate.id',
    `uid`               bigint(20)    NOT NULL COMMENT '助威用户uid',
    `biz_id`            int(11)       NOT NULL COMMENT '用户助威时所在频道业务ID，1-交友 2-语音房',
    `host_name`         char(32)      NOT NULL COMMENT '来源渠道APP，dreamer-Yo交友 yomi-Yo语音 tieba-贴吧 haokan-好看 browser-PC browsermobile-web',
    `used_channel`      int(11)       NOT NULL COMMENT '营收来源渠道',
    `sid`               bigint(20)    NOT NULL COMMENT '助威时所在频道sid',
    `ssid`              bigint(20)    NOT NULL COMMENT '助威时所在频道ssid',
    `total_cheer_prize` bigint(20)    NOT NULL DEFAULT 0 COMMENT '用户给这个活动动物总助威值 sum(om_round_cheer.cheer_value)',
    `target_uid`        bigint(20)    NOT NULL COMMENT '收礼人uid',
    `target_nick`       bigint(20)    NOT NULL COMMENT '收礼人昵称',
    `profit_uid`        bigint(20)    NOT NULL COMMENT '受益人UID',
    `create_time`       datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `prize_list_json`   varchar(1024) NOT NULL COMMENT '奖品数据，json数组格式：[{"prizeId":1,"propsId":1,"count":1}], 对应 om_prize 中的字段',
    `client_expand`     varchar(2048) NOT NULL DEFAULT '客户端扩展参数，送礼时候必须的',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_seq_id` (`seq_id`),
    INDEX `idx_round_candidate_uid_biz` (`round_id`, `uid`, `biz_id`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT '用户本轮助威成功奖励列表记录 (服务治理添加按create_time 周期性清理)';


-- 用户本轮助威碎片获得记录 结算的时候，不管是否助威成功，都会发放装扮碎片
CREATE TABLE IF NOT EXISTS `hd_sports`.`om_round_reward_frag`
(
    `id`          bigint(20) AUTO_INCREMENT COMMENT '自增唯一ID',
    `seq_id`      char(64)   NOT NULL COMMENT '送礼流水号，唯一流水号',
    `round_id`    bigint(20) NOT NULL COMMENT '轮次ID，关联 om_round.round_id',
    `uid`         bigint(20) NOT NULL COMMENT '助威用户uid',
    `biz_id`      int(11)    NOT NULL COMMENT '用户助威时所在频道业务ID，1-交友 2-语音房',
    `sid`         bigint(20) NOT NULL COMMENT '助威时所在频道sid',
    `ssid`        bigint(20) NOT NULL COMMENT '助威时所在频道ssid',
    `frag_count`  bigint(20) NOT NULL COMMENT '奖励的碎片数量',
    `create_time` datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_seq_id` (`seq_id`),
    INDEX `idx_round_candidate_uid_biz` (`round_id`, `uid`, `biz_id`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT '用户本轮助威碎片获得记录 结算的时候，不管是否助威成功，都会发放装扮碎片 (服务治理添加按create_time 周期性清理)';

-- 购买果实券（灵气券）,助力用户记录
CREATE TABLE IF NOT EXISTS `hd_sports`.`om_buy_currency_record`
(
    `id`           bigint(20) AUTO_INCREMENT COMMENT '自增唯一ID',
    `seq_id`       char(64)   NOT NULL COMMENT '位移流水号',
    `uid`          bigint(20) NOT NULL COMMENT '助力用户uid',
    `biz_id`       int(11)    NOT NULL COMMENT '用户助力时所在频道业务ID，1-交友 2-语音房',
    `host_name`    char(32)   NOT NULL COMMENT '来源渠道APP，dreamer-Yo交友 yomi-Yo语音 tieba-贴吧 haokan-好看 browser-PC browsermobile-web',
    `used_channel` int(11)    NOT NULL COMMENT '营收来源渠道',
    `sid`          bigint(20) NOT NULL COMMENT '助力时所在频道sid',
    `ssid`         bigint(20) NOT NULL COMMENT '助力时所在频道ssid',
    `be_help_uid`  bigint(20) NOT NULL COMMENT '被助力的uid',
    `help_count`   bigint(20) NOT NULL COMMENT '助力数量',
    `help_amount`  bigint(20) NOT NULL COMMENT '助力金额（转换为特定业务的价值，比如交友-紫水晶，语音房-金钻）',
    `fruit_count`  bigint(20) NOT NULL COMMENT '本次获取的果实数量',
    `help_value`   bigint(20) NOT NULL COMMENT '本次助力给被助力uid累计的助力值',
    `create_time`  datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_seq_id` (`seq_id`),
    INDEX `idx_uid` (`uid`),
    INDEX `idx_help_uid` (`be_help_uid`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT '购买果实券（灵气券）,助力用户记录 (服务治理添加按create_time 周期性清理)';

-- 被助力用户榜单
CREATE TABLE IF NOT EXISTS `hd_sports`.`om_be_help_rank`
(
    `id`          bigint(20) AUTO_INCREMENT COMMENT '自增唯一ID',
    `dt`          int(11)    NOT NULL COMMENT '统计日期 yyyyMMdd 格式',
    `uid`         bigint(20) NOT NULL COMMENT '助力用户uid',
    `help_value`  bigint(20) NOT NULL COMMENT '本次助力给被助力uid累计的助力值',
    `create_time` datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后一次助力值更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid` (`uid`),
    INDEX `idx_create_time` (`create_time`),
    INDEX `idx_update_time` (`update_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT '被助力用户榜单 (服务治理添加按create_time 周期性清理)';

-- ------------------------------------------- USER CASE ------------------------------------------------------------------------- --
-- 获取本场次信息
-- 1. 先查询 select * from om_round where round_id = ?;
-- 2. 不存在的话，程序中计算 init_offset, config_snapshot 然后使用 insert into .... on duplicate update xxx
-- 3. 如果插入失败，那么重新从主库（注意，强制限定从主库）查询一次 获取最新值

-- 获取玩法规则配置信息
-- 1. 先【获取本场次信息】 2. 获取om_candidate基础配置，更换权重 3. 获取奖励配置信息om_prize

-- 获取用户本场助威信息
-- 1. 获取用户本轮助威明细记录 select * from om_round_cheer where round_id = ? and uid = ?
-- 2. 根据结果计算响应

-- 获取用户最近7天的助威历史记录
-- 1. 获取用户最近 create_time >（7天 - 80秒） 的数据 select * from om_round_cheer where create_time > $minCreateTime and uid = ? order by id
-- 2. 获取每轮的获胜动物 select round_id,winner from om_round where round_id in (userRoundIds)
-- 3. 获取每轮的奖励记录 select * from om_round_reward_prize where round_id in (userRoundIds)
-- 4. 获取每轮的碎片记录 select * from om_round_reward_frag where round_in in (userRoundIds)

-- 获取上一场助威冠军信息
-- 1. 获取上一场的 roundId select * from om_round order by id desc limit 1, 1;
-- 2. champion_uid 就是助威冠军信息

-- 获取用户本场助威信息
-- 1. 获取本场次信息 参考第一个 CASE
-- 2. 参考 【获取用户最近7天的助威历史记录】

-- 获取近10场运动会夺冠记录
-- 1. select * from om_round order by id desc limit 11
-- 2. 取出11 个，是防止最新一场还没结算

-- 获取助威成功排行版
-- 1. 获取最新一轮已经结算的场次信息 select t.* from (select * from om_round order by id desc limit 2) t where t.status = 3
-- 2. select * from om_round_reward_prize where round_id = ? order by total_cheer_prize desc limit 3

-- 获取最近30条奖励大于等于500元的记录
-- 1. select * from om_round_reward_prize order by total_cheer_prize desc limit 30

-- 获取被助力用户榜单
-- 1. select * from om_be_help_rank where dt = $today order by help_value desc , update_time asc limit 30













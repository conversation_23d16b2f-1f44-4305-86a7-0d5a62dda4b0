package com.yy.hd.client;

import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 *
 * <AUTHOR>
 * @since 2025/6/26 15:55
 */
@SpringBootTest(classes = {TurnoverClient.class})
@EnableDubbo
@Slf4j
class TurnoverClientTest {

    @Autowired
    TurnoverClient client;

    @Test
    void doImThriftServivce() throws Exception {
        Assertions.assertNotNull(client);
        client.imService.ping2();
    }
}
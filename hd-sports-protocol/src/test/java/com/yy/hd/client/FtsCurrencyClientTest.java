package com.yy.hd.client;

import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 *
 * <AUTHOR>
 * @since 2025/6/27 16:42
 */
@SpringBootTest(classes = {FtsCurrencyClient.class})
@EnableDubbo
@Slf4j
class FtsCurrencyClientTest {

    @Autowired
    FtsCurrencyClient client;

    @Test
    void ping() throws Exception {
        Assertions.assertNotNull(client);
        client.ping();
    }
}
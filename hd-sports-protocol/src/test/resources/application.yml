spring:
  application:
    name: yrpc-consumer
  main:
    allow-bean-definition-overriding: true
  profiles:
    active: de
dubbo:
  #客户端配置
  #consumer：是refer的公共默认配置，用于控制RPC访问等
  #		#鹰眼，请务必配置
  #		1.配置鹰眼信息，推荐使用，请务必配置，其配置点是：
  #			1）filter="eagle"：必须配置
  #			2）eagle.appname：必须配置，当前运营上报鹰眼的应用名，用于在运维metrics展示归类，代表一个项目
  #				consumer.parameter.eagle.appname value=${yourAppName}
  #		2.配置鹰眼信息后，可以1)自动上报服务访问metrics指标；2)上报框架运行时一些监控信息；
  #		3.若业务有自己独立的鹰眼体系不期望使用框架自带的，可配置eagle.enable=false即可关闭2 1)能力；如下：
  #		  consumer.parameter.eagle.enable value="false"
  #
  #		#路由负载均衡，请务必配置
  #		1. lbrouters="room,isp"，代表采用同机房同运营商ISP优先路由策略
  #		2. loadbalance="smart"，代表采用智能负载均衡策略，一般无需调整
  #		3. 更多使用说明参考：
  consumer:
    timeout: 3000 # 设置默认超时时间
    parameters:
      eagle:
        enable: false
        appname: yrpc_springboot_consumer  #鹰眼应用名
    filter: eagle
    loadbalance: smart
    lbrouters: room,isp
  #注册中心配置，目前仅支持s2s注册中心，
  registries:
    client-reg:
      id: client-reg
      address: s2s://test-wudang-meta.yy.com #s2s注册中心，生产环境可以配置 s2s://meta.yy.com
      username: local_server1 #s2s服务名，即s2s name
      password: f401ab5642af24ccef475718c8c9b38c1814b42745890c8d37bdce7d0459b551  #s2s accesskey
      register: false
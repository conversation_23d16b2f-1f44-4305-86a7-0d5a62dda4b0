/**
 * Autogenerated by Thrift Compiler (0.11.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.hd.api.thrift.fts.currency;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.11.0)", date = "2025-06-27")
public class TReverseFragReq implements org.apache.thrift.TBase<TReverseFragReq, TReverseFragReq._Fields>, java.io.Serializable, Cloneable, Comparable<TReverseFragReq> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("TReverseFragReq");

  private static final org.apache.thrift.protocol.TField UID_FIELD_DESC = new org.apache.thrift.protocol.TField("uid", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField SEQ_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("seqId", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField FRAG_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("fragType", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField APP_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("appId", org.apache.thrift.protocol.TType.I64, (short)4);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new TReverseFragReqStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new TReverseFragReqTupleSchemeFactory();

  public long uid; // required
  public java.lang.String seqId; // required
  public long fragType; // required
  public long appId; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    UID((short)1, "uid"),
    SEQ_ID((short)2, "seqId"),
    FRAG_TYPE((short)3, "fragType"),
    APP_ID((short)4, "appId");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // UID
          return UID;
        case 2: // SEQ_ID
          return SEQ_ID;
        case 3: // FRAG_TYPE
          return FRAG_TYPE;
        case 4: // APP_ID
          return APP_ID;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __UID_ISSET_ID = 0;
  private static final int __FRAGTYPE_ISSET_ID = 1;
  private static final int __APPID_ISSET_ID = 2;
  private byte __isset_bitfield = 0;
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.UID, new org.apache.thrift.meta_data.FieldMetaData("uid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SEQ_ID, new org.apache.thrift.meta_data.FieldMetaData("seqId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.FRAG_TYPE, new org.apache.thrift.meta_data.FieldMetaData("fragType", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.APP_ID, new org.apache.thrift.meta_data.FieldMetaData("appId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(TReverseFragReq.class, metaDataMap);
  }

  public TReverseFragReq() {
  }

  public TReverseFragReq(
    long uid,
    java.lang.String seqId,
    long fragType,
    long appId)
  {
    this();
    this.uid = uid;
    setUidIsSet(true);
    this.seqId = seqId;
    this.fragType = fragType;
    setFragTypeIsSet(true);
    this.appId = appId;
    setAppIdIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public TReverseFragReq(TReverseFragReq other) {
    __isset_bitfield = other.__isset_bitfield;
    this.uid = other.uid;
    if (other.isSetSeqId()) {
      this.seqId = other.seqId;
    }
    this.fragType = other.fragType;
    this.appId = other.appId;
  }

  public TReverseFragReq deepCopy() {
    return new TReverseFragReq(this);
  }

  @Override
  public void clear() {
    setUidIsSet(false);
    this.uid = 0;
    this.seqId = null;
    setFragTypeIsSet(false);
    this.fragType = 0;
    setAppIdIsSet(false);
    this.appId = 0;
  }

  public long getUid() {
    return this.uid;
  }

  public TReverseFragReq setUid(long uid) {
    this.uid = uid;
    setUidIsSet(true);
    return this;
  }

  public void unsetUid() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __UID_ISSET_ID);
  }

  /** Returns true if field uid is set (has been assigned a value) and false otherwise */
  public boolean isSetUid() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __UID_ISSET_ID);
  }

  public void setUidIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __UID_ISSET_ID, value);
  }

  public java.lang.String getSeqId() {
    return this.seqId;
  }

  public TReverseFragReq setSeqId(java.lang.String seqId) {
    this.seqId = seqId;
    return this;
  }

  public void unsetSeqId() {
    this.seqId = null;
  }

  /** Returns true if field seqId is set (has been assigned a value) and false otherwise */
  public boolean isSetSeqId() {
    return this.seqId != null;
  }

  public void setSeqIdIsSet(boolean value) {
    if (!value) {
      this.seqId = null;
    }
  }

  public long getFragType() {
    return this.fragType;
  }

  public TReverseFragReq setFragType(long fragType) {
    this.fragType = fragType;
    setFragTypeIsSet(true);
    return this;
  }

  public void unsetFragType() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __FRAGTYPE_ISSET_ID);
  }

  /** Returns true if field fragType is set (has been assigned a value) and false otherwise */
  public boolean isSetFragType() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __FRAGTYPE_ISSET_ID);
  }

  public void setFragTypeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __FRAGTYPE_ISSET_ID, value);
  }

  public long getAppId() {
    return this.appId;
  }

  public TReverseFragReq setAppId(long appId) {
    this.appId = appId;
    setAppIdIsSet(true);
    return this;
  }

  public void unsetAppId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __APPID_ISSET_ID);
  }

  /** Returns true if field appId is set (has been assigned a value) and false otherwise */
  public boolean isSetAppId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __APPID_ISSET_ID);
  }

  public void setAppIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __APPID_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, java.lang.Object value) {
    switch (field) {
    case UID:
      if (value == null) {
        unsetUid();
      } else {
        setUid((java.lang.Long)value);
      }
      break;

    case SEQ_ID:
      if (value == null) {
        unsetSeqId();
      } else {
        setSeqId((java.lang.String)value);
      }
      break;

    case FRAG_TYPE:
      if (value == null) {
        unsetFragType();
      } else {
        setFragType((java.lang.Long)value);
      }
      break;

    case APP_ID:
      if (value == null) {
        unsetAppId();
      } else {
        setAppId((java.lang.Long)value);
      }
      break;

    }
  }

  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case UID:
      return getUid();

    case SEQ_ID:
      return getSeqId();

    case FRAG_TYPE:
      return getFragType();

    case APP_ID:
      return getAppId();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case UID:
      return isSetUid();
    case SEQ_ID:
      return isSetSeqId();
    case FRAG_TYPE:
      return isSetFragType();
    case APP_ID:
      return isSetAppId();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that == null)
      return false;
    if (that instanceof TReverseFragReq)
      return this.equals((TReverseFragReq)that);
    return false;
  }

  public boolean equals(TReverseFragReq that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_uid = true;
    boolean that_present_uid = true;
    if (this_present_uid || that_present_uid) {
      if (!(this_present_uid && that_present_uid))
        return false;
      if (this.uid != that.uid)
        return false;
    }

    boolean this_present_seqId = true && this.isSetSeqId();
    boolean that_present_seqId = true && that.isSetSeqId();
    if (this_present_seqId || that_present_seqId) {
      if (!(this_present_seqId && that_present_seqId))
        return false;
      if (!this.seqId.equals(that.seqId))
        return false;
    }

    boolean this_present_fragType = true;
    boolean that_present_fragType = true;
    if (this_present_fragType || that_present_fragType) {
      if (!(this_present_fragType && that_present_fragType))
        return false;
      if (this.fragType != that.fragType)
        return false;
    }

    boolean this_present_appId = true;
    boolean that_present_appId = true;
    if (this_present_appId || that_present_appId) {
      if (!(this_present_appId && that_present_appId))
        return false;
      if (this.appId != that.appId)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(uid);

    hashCode = hashCode * 8191 + ((isSetSeqId()) ? 131071 : 524287);
    if (isSetSeqId())
      hashCode = hashCode * 8191 + seqId.hashCode();

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(fragType);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(appId);

    return hashCode;
  }

  @Override
  public int compareTo(TReverseFragReq other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.valueOf(isSetUid()).compareTo(other.isSetUid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.uid, other.uid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetSeqId()).compareTo(other.isSetSeqId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSeqId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.seqId, other.seqId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetFragType()).compareTo(other.isSetFragType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetFragType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.fragType, other.fragType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetAppId()).compareTo(other.isSetAppId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAppId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.appId, other.appId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("TReverseFragReq(");
    boolean first = true;

    sb.append("uid:");
    sb.append(this.uid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("seqId:");
    if (this.seqId == null) {
      sb.append("null");
    } else {
      sb.append(this.seqId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("fragType:");
    sb.append(this.fragType);
    first = false;
    if (!first) sb.append(", ");
    sb.append("appId:");
    sb.append(this.appId);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class TReverseFragReqStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public TReverseFragReqStandardScheme getScheme() {
      return new TReverseFragReqStandardScheme();
    }
  }

  private static class TReverseFragReqStandardScheme extends org.apache.thrift.scheme.StandardScheme<TReverseFragReq> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, TReverseFragReq struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // UID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.uid = iprot.readI64();
              struct.setUidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // SEQ_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.seqId = iprot.readString();
              struct.setSeqIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // FRAG_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.fragType = iprot.readI64();
              struct.setFragTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // APP_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.appId = iprot.readI64();
              struct.setAppIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, TReverseFragReq struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(UID_FIELD_DESC);
      oprot.writeI64(struct.uid);
      oprot.writeFieldEnd();
      if (struct.seqId != null) {
        oprot.writeFieldBegin(SEQ_ID_FIELD_DESC);
        oprot.writeString(struct.seqId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(FRAG_TYPE_FIELD_DESC);
      oprot.writeI64(struct.fragType);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(APP_ID_FIELD_DESC);
      oprot.writeI64(struct.appId);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class TReverseFragReqTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public TReverseFragReqTupleScheme getScheme() {
      return new TReverseFragReqTupleScheme();
    }
  }

  private static class TReverseFragReqTupleScheme extends org.apache.thrift.scheme.TupleScheme<TReverseFragReq> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, TReverseFragReq struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetUid()) {
        optionals.set(0);
      }
      if (struct.isSetSeqId()) {
        optionals.set(1);
      }
      if (struct.isSetFragType()) {
        optionals.set(2);
      }
      if (struct.isSetAppId()) {
        optionals.set(3);
      }
      oprot.writeBitSet(optionals, 4);
      if (struct.isSetUid()) {
        oprot.writeI64(struct.uid);
      }
      if (struct.isSetSeqId()) {
        oprot.writeString(struct.seqId);
      }
      if (struct.isSetFragType()) {
        oprot.writeI64(struct.fragType);
      }
      if (struct.isSetAppId()) {
        oprot.writeI64(struct.appId);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, TReverseFragReq struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(4);
      if (incoming.get(0)) {
        struct.uid = iprot.readI64();
        struct.setUidIsSet(true);
      }
      if (incoming.get(1)) {
        struct.seqId = iprot.readString();
        struct.setSeqIdIsSet(true);
      }
      if (incoming.get(2)) {
        struct.fragType = iprot.readI64();
        struct.setFragTypeIsSet(true);
      }
      if (incoming.get(3)) {
        struct.appId = iprot.readI64();
        struct.setAppIdIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}


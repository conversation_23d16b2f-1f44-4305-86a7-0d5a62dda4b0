/**
 * Autogenerated by <PERSON>hrift Compiler (0.11.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.hd.api.thrift.fts.currency;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.11.0)", date = "2025-06-27")
public class TConsumeMagicBeanRequest implements org.apache.thrift.TBase<TConsumeMagicBeanRequest, TConsumeMagicBeanRequest._Fields>, java.io.Serializable, Cloneable, Comparable<TConsumeMagicBeanRequest> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("TConsumeMagicBeanRequest");

  private static final org.apache.thrift.protocol.TField UID_FIELD_DESC = new org.apache.thrift.protocol.TField("uid", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField ANCHOR_UID_FIELD_DESC = new org.apache.thrift.protocol.TField("anchorUid", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField SID_FIELD_DESC = new org.apache.thrift.protocol.TField("sid", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField SSID_FIELD_DESC = new org.apache.thrift.protocol.TField("ssid", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField COUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("count", org.apache.thrift.protocol.TType.I64, (short)5);
  private static final org.apache.thrift.protocol.TField PRODUCT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("productId", org.apache.thrift.protocol.TType.I32, (short)6);
  private static final org.apache.thrift.protocol.TField PLATFORM_FIELD_DESC = new org.apache.thrift.protocol.TField("platform", org.apache.thrift.protocol.TType.I32, (short)7);
  private static final org.apache.thrift.protocol.TField TARGET_UID_FIELD_DESC = new org.apache.thrift.protocol.TField("targetUid", org.apache.thrift.protocol.TType.I64, (short)8);
  private static final org.apache.thrift.protocol.TField SEQ_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("seqId", org.apache.thrift.protocol.TType.STRING, (short)9);
  private static final org.apache.thrift.protocol.TField DESCRIPTION_FIELD_DESC = new org.apache.thrift.protocol.TField("description", org.apache.thrift.protocol.TType.STRING, (short)10);
  private static final org.apache.thrift.protocol.TField TIMESTAMP_FIELD_DESC = new org.apache.thrift.protocol.TField("timestamp", org.apache.thrift.protocol.TType.I64, (short)11);
  private static final org.apache.thrift.protocol.TField EXPAND_FIELD_DESC = new org.apache.thrift.protocol.TField("expand", org.apache.thrift.protocol.TType.STRING, (short)12);
  private static final org.apache.thrift.protocol.TField CONSUME_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("consumeType", org.apache.thrift.protocol.TType.I32, (short)13);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new TConsumeMagicBeanRequestStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new TConsumeMagicBeanRequestTupleSchemeFactory();

  public long uid; // required
  public long anchorUid; // required
  public long sid; // required
  public long ssid; // required
  public long count; // required
  /**
   * 
   * @see ProductType
   */
  public ProductType productId; // required
  /**
   * 
   * @see TPlatform
   */
  public TPlatform platform; // required
  public long targetUid; // required
  public java.lang.String seqId; // required
  public java.lang.String description; // required
  public long timestamp; // required
  public java.lang.String expand; // required
  /**
   * 
   * @see MagicBeanConsumeType
   */
  public MagicBeanConsumeType consumeType; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    UID((short)1, "uid"),
    ANCHOR_UID((short)2, "anchorUid"),
    SID((short)3, "sid"),
    SSID((short)4, "ssid"),
    COUNT((short)5, "count"),
    /**
     * 
     * @see ProductType
     */
    PRODUCT_ID((short)6, "productId"),
    /**
     * 
     * @see TPlatform
     */
    PLATFORM((short)7, "platform"),
    TARGET_UID((short)8, "targetUid"),
    SEQ_ID((short)9, "seqId"),
    DESCRIPTION((short)10, "description"),
    TIMESTAMP((short)11, "timestamp"),
    EXPAND((short)12, "expand"),
    /**
     * 
     * @see MagicBeanConsumeType
     */
    CONSUME_TYPE((short)13, "consumeType");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // UID
          return UID;
        case 2: // ANCHOR_UID
          return ANCHOR_UID;
        case 3: // SID
          return SID;
        case 4: // SSID
          return SSID;
        case 5: // COUNT
          return COUNT;
        case 6: // PRODUCT_ID
          return PRODUCT_ID;
        case 7: // PLATFORM
          return PLATFORM;
        case 8: // TARGET_UID
          return TARGET_UID;
        case 9: // SEQ_ID
          return SEQ_ID;
        case 10: // DESCRIPTION
          return DESCRIPTION;
        case 11: // TIMESTAMP
          return TIMESTAMP;
        case 12: // EXPAND
          return EXPAND;
        case 13: // CONSUME_TYPE
          return CONSUME_TYPE;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __UID_ISSET_ID = 0;
  private static final int __ANCHORUID_ISSET_ID = 1;
  private static final int __SID_ISSET_ID = 2;
  private static final int __SSID_ISSET_ID = 3;
  private static final int __COUNT_ISSET_ID = 4;
  private static final int __TARGETUID_ISSET_ID = 5;
  private static final int __TIMESTAMP_ISSET_ID = 6;
  private byte __isset_bitfield = 0;
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.UID, new org.apache.thrift.meta_data.FieldMetaData("uid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ANCHOR_UID, new org.apache.thrift.meta_data.FieldMetaData("anchorUid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SID, new org.apache.thrift.meta_data.FieldMetaData("sid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SSID, new org.apache.thrift.meta_data.FieldMetaData("ssid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.COUNT, new org.apache.thrift.meta_data.FieldMetaData("count", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PRODUCT_ID, new org.apache.thrift.meta_data.FieldMetaData("productId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, ProductType.class)));
    tmpMap.put(_Fields.PLATFORM, new org.apache.thrift.meta_data.FieldMetaData("platform", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, TPlatform.class)));
    tmpMap.put(_Fields.TARGET_UID, new org.apache.thrift.meta_data.FieldMetaData("targetUid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SEQ_ID, new org.apache.thrift.meta_data.FieldMetaData("seqId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.DESCRIPTION, new org.apache.thrift.meta_data.FieldMetaData("description", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.TIMESTAMP, new org.apache.thrift.meta_data.FieldMetaData("timestamp", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.EXPAND, new org.apache.thrift.meta_data.FieldMetaData("expand", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.CONSUME_TYPE, new org.apache.thrift.meta_data.FieldMetaData("consumeType", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, MagicBeanConsumeType.class)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(TConsumeMagicBeanRequest.class, metaDataMap);
  }

  public TConsumeMagicBeanRequest() {
  }

  public TConsumeMagicBeanRequest(
    long uid,
    long anchorUid,
    long sid,
    long ssid,
    long count,
    ProductType productId,
    TPlatform platform,
    long targetUid,
    java.lang.String seqId,
    java.lang.String description,
    long timestamp,
    java.lang.String expand,
    MagicBeanConsumeType consumeType)
  {
    this();
    this.uid = uid;
    setUidIsSet(true);
    this.anchorUid = anchorUid;
    setAnchorUidIsSet(true);
    this.sid = sid;
    setSidIsSet(true);
    this.ssid = ssid;
    setSsidIsSet(true);
    this.count = count;
    setCountIsSet(true);
    this.productId = productId;
    this.platform = platform;
    this.targetUid = targetUid;
    setTargetUidIsSet(true);
    this.seqId = seqId;
    this.description = description;
    this.timestamp = timestamp;
    setTimestampIsSet(true);
    this.expand = expand;
    this.consumeType = consumeType;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public TConsumeMagicBeanRequest(TConsumeMagicBeanRequest other) {
    __isset_bitfield = other.__isset_bitfield;
    this.uid = other.uid;
    this.anchorUid = other.anchorUid;
    this.sid = other.sid;
    this.ssid = other.ssid;
    this.count = other.count;
    if (other.isSetProductId()) {
      this.productId = other.productId;
    }
    if (other.isSetPlatform()) {
      this.platform = other.platform;
    }
    this.targetUid = other.targetUid;
    if (other.isSetSeqId()) {
      this.seqId = other.seqId;
    }
    if (other.isSetDescription()) {
      this.description = other.description;
    }
    this.timestamp = other.timestamp;
    if (other.isSetExpand()) {
      this.expand = other.expand;
    }
    if (other.isSetConsumeType()) {
      this.consumeType = other.consumeType;
    }
  }

  public TConsumeMagicBeanRequest deepCopy() {
    return new TConsumeMagicBeanRequest(this);
  }

  @Override
  public void clear() {
    setUidIsSet(false);
    this.uid = 0;
    setAnchorUidIsSet(false);
    this.anchorUid = 0;
    setSidIsSet(false);
    this.sid = 0;
    setSsidIsSet(false);
    this.ssid = 0;
    setCountIsSet(false);
    this.count = 0;
    this.productId = null;
    this.platform = null;
    setTargetUidIsSet(false);
    this.targetUid = 0;
    this.seqId = null;
    this.description = null;
    setTimestampIsSet(false);
    this.timestamp = 0;
    this.expand = null;
    this.consumeType = null;
  }

  public long getUid() {
    return this.uid;
  }

  public TConsumeMagicBeanRequest setUid(long uid) {
    this.uid = uid;
    setUidIsSet(true);
    return this;
  }

  public void unsetUid() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __UID_ISSET_ID);
  }

  /** Returns true if field uid is set (has been assigned a value) and false otherwise */
  public boolean isSetUid() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __UID_ISSET_ID);
  }

  public void setUidIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __UID_ISSET_ID, value);
  }

  public long getAnchorUid() {
    return this.anchorUid;
  }

  public TConsumeMagicBeanRequest setAnchorUid(long anchorUid) {
    this.anchorUid = anchorUid;
    setAnchorUidIsSet(true);
    return this;
  }

  public void unsetAnchorUid() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ANCHORUID_ISSET_ID);
  }

  /** Returns true if field anchorUid is set (has been assigned a value) and false otherwise */
  public boolean isSetAnchorUid() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ANCHORUID_ISSET_ID);
  }

  public void setAnchorUidIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ANCHORUID_ISSET_ID, value);
  }

  public long getSid() {
    return this.sid;
  }

  public TConsumeMagicBeanRequest setSid(long sid) {
    this.sid = sid;
    setSidIsSet(true);
    return this;
  }

  public void unsetSid() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __SID_ISSET_ID);
  }

  /** Returns true if field sid is set (has been assigned a value) and false otherwise */
  public boolean isSetSid() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __SID_ISSET_ID);
  }

  public void setSidIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __SID_ISSET_ID, value);
  }

  public long getSsid() {
    return this.ssid;
  }

  public TConsumeMagicBeanRequest setSsid(long ssid) {
    this.ssid = ssid;
    setSsidIsSet(true);
    return this;
  }

  public void unsetSsid() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __SSID_ISSET_ID);
  }

  /** Returns true if field ssid is set (has been assigned a value) and false otherwise */
  public boolean isSetSsid() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __SSID_ISSET_ID);
  }

  public void setSsidIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __SSID_ISSET_ID, value);
  }

  public long getCount() {
    return this.count;
  }

  public TConsumeMagicBeanRequest setCount(long count) {
    this.count = count;
    setCountIsSet(true);
    return this;
  }

  public void unsetCount() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __COUNT_ISSET_ID);
  }

  /** Returns true if field count is set (has been assigned a value) and false otherwise */
  public boolean isSetCount() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __COUNT_ISSET_ID);
  }

  public void setCountIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __COUNT_ISSET_ID, value);
  }

  /**
   * 
   * @see ProductType
   */
  public ProductType getProductId() {
    return this.productId;
  }

  /**
   * 
   * @see ProductType
   */
  public TConsumeMagicBeanRequest setProductId(ProductType productId) {
    this.productId = productId;
    return this;
  }

  public void unsetProductId() {
    this.productId = null;
  }

  /** Returns true if field productId is set (has been assigned a value) and false otherwise */
  public boolean isSetProductId() {
    return this.productId != null;
  }

  public void setProductIdIsSet(boolean value) {
    if (!value) {
      this.productId = null;
    }
  }

  /**
   * 
   * @see TPlatform
   */
  public TPlatform getPlatform() {
    return this.platform;
  }

  /**
   * 
   * @see TPlatform
   */
  public TConsumeMagicBeanRequest setPlatform(TPlatform platform) {
    this.platform = platform;
    return this;
  }

  public void unsetPlatform() {
    this.platform = null;
  }

  /** Returns true if field platform is set (has been assigned a value) and false otherwise */
  public boolean isSetPlatform() {
    return this.platform != null;
  }

  public void setPlatformIsSet(boolean value) {
    if (!value) {
      this.platform = null;
    }
  }

  public long getTargetUid() {
    return this.targetUid;
  }

  public TConsumeMagicBeanRequest setTargetUid(long targetUid) {
    this.targetUid = targetUid;
    setTargetUidIsSet(true);
    return this;
  }

  public void unsetTargetUid() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __TARGETUID_ISSET_ID);
  }

  /** Returns true if field targetUid is set (has been assigned a value) and false otherwise */
  public boolean isSetTargetUid() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __TARGETUID_ISSET_ID);
  }

  public void setTargetUidIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __TARGETUID_ISSET_ID, value);
  }

  public java.lang.String getSeqId() {
    return this.seqId;
  }

  public TConsumeMagicBeanRequest setSeqId(java.lang.String seqId) {
    this.seqId = seqId;
    return this;
  }

  public void unsetSeqId() {
    this.seqId = null;
  }

  /** Returns true if field seqId is set (has been assigned a value) and false otherwise */
  public boolean isSetSeqId() {
    return this.seqId != null;
  }

  public void setSeqIdIsSet(boolean value) {
    if (!value) {
      this.seqId = null;
    }
  }

  public java.lang.String getDescription() {
    return this.description;
  }

  public TConsumeMagicBeanRequest setDescription(java.lang.String description) {
    this.description = description;
    return this;
  }

  public void unsetDescription() {
    this.description = null;
  }

  /** Returns true if field description is set (has been assigned a value) and false otherwise */
  public boolean isSetDescription() {
    return this.description != null;
  }

  public void setDescriptionIsSet(boolean value) {
    if (!value) {
      this.description = null;
    }
  }

  public long getTimestamp() {
    return this.timestamp;
  }

  public TConsumeMagicBeanRequest setTimestamp(long timestamp) {
    this.timestamp = timestamp;
    setTimestampIsSet(true);
    return this;
  }

  public void unsetTimestamp() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __TIMESTAMP_ISSET_ID);
  }

  /** Returns true if field timestamp is set (has been assigned a value) and false otherwise */
  public boolean isSetTimestamp() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __TIMESTAMP_ISSET_ID);
  }

  public void setTimestampIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __TIMESTAMP_ISSET_ID, value);
  }

  public java.lang.String getExpand() {
    return this.expand;
  }

  public TConsumeMagicBeanRequest setExpand(java.lang.String expand) {
    this.expand = expand;
    return this;
  }

  public void unsetExpand() {
    this.expand = null;
  }

  /** Returns true if field expand is set (has been assigned a value) and false otherwise */
  public boolean isSetExpand() {
    return this.expand != null;
  }

  public void setExpandIsSet(boolean value) {
    if (!value) {
      this.expand = null;
    }
  }

  /**
   * 
   * @see MagicBeanConsumeType
   */
  public MagicBeanConsumeType getConsumeType() {
    return this.consumeType;
  }

  /**
   * 
   * @see MagicBeanConsumeType
   */
  public TConsumeMagicBeanRequest setConsumeType(MagicBeanConsumeType consumeType) {
    this.consumeType = consumeType;
    return this;
  }

  public void unsetConsumeType() {
    this.consumeType = null;
  }

  /** Returns true if field consumeType is set (has been assigned a value) and false otherwise */
  public boolean isSetConsumeType() {
    return this.consumeType != null;
  }

  public void setConsumeTypeIsSet(boolean value) {
    if (!value) {
      this.consumeType = null;
    }
  }

  public void setFieldValue(_Fields field, java.lang.Object value) {
    switch (field) {
    case UID:
      if (value == null) {
        unsetUid();
      } else {
        setUid((java.lang.Long)value);
      }
      break;

    case ANCHOR_UID:
      if (value == null) {
        unsetAnchorUid();
      } else {
        setAnchorUid((java.lang.Long)value);
      }
      break;

    case SID:
      if (value == null) {
        unsetSid();
      } else {
        setSid((java.lang.Long)value);
      }
      break;

    case SSID:
      if (value == null) {
        unsetSsid();
      } else {
        setSsid((java.lang.Long)value);
      }
      break;

    case COUNT:
      if (value == null) {
        unsetCount();
      } else {
        setCount((java.lang.Long)value);
      }
      break;

    case PRODUCT_ID:
      if (value == null) {
        unsetProductId();
      } else {
        setProductId((ProductType)value);
      }
      break;

    case PLATFORM:
      if (value == null) {
        unsetPlatform();
      } else {
        setPlatform((TPlatform)value);
      }
      break;

    case TARGET_UID:
      if (value == null) {
        unsetTargetUid();
      } else {
        setTargetUid((java.lang.Long)value);
      }
      break;

    case SEQ_ID:
      if (value == null) {
        unsetSeqId();
      } else {
        setSeqId((java.lang.String)value);
      }
      break;

    case DESCRIPTION:
      if (value == null) {
        unsetDescription();
      } else {
        setDescription((java.lang.String)value);
      }
      break;

    case TIMESTAMP:
      if (value == null) {
        unsetTimestamp();
      } else {
        setTimestamp((java.lang.Long)value);
      }
      break;

    case EXPAND:
      if (value == null) {
        unsetExpand();
      } else {
        setExpand((java.lang.String)value);
      }
      break;

    case CONSUME_TYPE:
      if (value == null) {
        unsetConsumeType();
      } else {
        setConsumeType((MagicBeanConsumeType)value);
      }
      break;

    }
  }

  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case UID:
      return getUid();

    case ANCHOR_UID:
      return getAnchorUid();

    case SID:
      return getSid();

    case SSID:
      return getSsid();

    case COUNT:
      return getCount();

    case PRODUCT_ID:
      return getProductId();

    case PLATFORM:
      return getPlatform();

    case TARGET_UID:
      return getTargetUid();

    case SEQ_ID:
      return getSeqId();

    case DESCRIPTION:
      return getDescription();

    case TIMESTAMP:
      return getTimestamp();

    case EXPAND:
      return getExpand();

    case CONSUME_TYPE:
      return getConsumeType();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case UID:
      return isSetUid();
    case ANCHOR_UID:
      return isSetAnchorUid();
    case SID:
      return isSetSid();
    case SSID:
      return isSetSsid();
    case COUNT:
      return isSetCount();
    case PRODUCT_ID:
      return isSetProductId();
    case PLATFORM:
      return isSetPlatform();
    case TARGET_UID:
      return isSetTargetUid();
    case SEQ_ID:
      return isSetSeqId();
    case DESCRIPTION:
      return isSetDescription();
    case TIMESTAMP:
      return isSetTimestamp();
    case EXPAND:
      return isSetExpand();
    case CONSUME_TYPE:
      return isSetConsumeType();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that == null)
      return false;
    if (that instanceof TConsumeMagicBeanRequest)
      return this.equals((TConsumeMagicBeanRequest)that);
    return false;
  }

  public boolean equals(TConsumeMagicBeanRequest that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_uid = true;
    boolean that_present_uid = true;
    if (this_present_uid || that_present_uid) {
      if (!(this_present_uid && that_present_uid))
        return false;
      if (this.uid != that.uid)
        return false;
    }

    boolean this_present_anchorUid = true;
    boolean that_present_anchorUid = true;
    if (this_present_anchorUid || that_present_anchorUid) {
      if (!(this_present_anchorUid && that_present_anchorUid))
        return false;
      if (this.anchorUid != that.anchorUid)
        return false;
    }

    boolean this_present_sid = true;
    boolean that_present_sid = true;
    if (this_present_sid || that_present_sid) {
      if (!(this_present_sid && that_present_sid))
        return false;
      if (this.sid != that.sid)
        return false;
    }

    boolean this_present_ssid = true;
    boolean that_present_ssid = true;
    if (this_present_ssid || that_present_ssid) {
      if (!(this_present_ssid && that_present_ssid))
        return false;
      if (this.ssid != that.ssid)
        return false;
    }

    boolean this_present_count = true;
    boolean that_present_count = true;
    if (this_present_count || that_present_count) {
      if (!(this_present_count && that_present_count))
        return false;
      if (this.count != that.count)
        return false;
    }

    boolean this_present_productId = true && this.isSetProductId();
    boolean that_present_productId = true && that.isSetProductId();
    if (this_present_productId || that_present_productId) {
      if (!(this_present_productId && that_present_productId))
        return false;
      if (!this.productId.equals(that.productId))
        return false;
    }

    boolean this_present_platform = true && this.isSetPlatform();
    boolean that_present_platform = true && that.isSetPlatform();
    if (this_present_platform || that_present_platform) {
      if (!(this_present_platform && that_present_platform))
        return false;
      if (!this.platform.equals(that.platform))
        return false;
    }

    boolean this_present_targetUid = true;
    boolean that_present_targetUid = true;
    if (this_present_targetUid || that_present_targetUid) {
      if (!(this_present_targetUid && that_present_targetUid))
        return false;
      if (this.targetUid != that.targetUid)
        return false;
    }

    boolean this_present_seqId = true && this.isSetSeqId();
    boolean that_present_seqId = true && that.isSetSeqId();
    if (this_present_seqId || that_present_seqId) {
      if (!(this_present_seqId && that_present_seqId))
        return false;
      if (!this.seqId.equals(that.seqId))
        return false;
    }

    boolean this_present_description = true && this.isSetDescription();
    boolean that_present_description = true && that.isSetDescription();
    if (this_present_description || that_present_description) {
      if (!(this_present_description && that_present_description))
        return false;
      if (!this.description.equals(that.description))
        return false;
    }

    boolean this_present_timestamp = true;
    boolean that_present_timestamp = true;
    if (this_present_timestamp || that_present_timestamp) {
      if (!(this_present_timestamp && that_present_timestamp))
        return false;
      if (this.timestamp != that.timestamp)
        return false;
    }

    boolean this_present_expand = true && this.isSetExpand();
    boolean that_present_expand = true && that.isSetExpand();
    if (this_present_expand || that_present_expand) {
      if (!(this_present_expand && that_present_expand))
        return false;
      if (!this.expand.equals(that.expand))
        return false;
    }

    boolean this_present_consumeType = true && this.isSetConsumeType();
    boolean that_present_consumeType = true && that.isSetConsumeType();
    if (this_present_consumeType || that_present_consumeType) {
      if (!(this_present_consumeType && that_present_consumeType))
        return false;
      if (!this.consumeType.equals(that.consumeType))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(uid);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(anchorUid);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(sid);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(ssid);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(count);

    hashCode = hashCode * 8191 + ((isSetProductId()) ? 131071 : 524287);
    if (isSetProductId())
      hashCode = hashCode * 8191 + productId.getValue();

    hashCode = hashCode * 8191 + ((isSetPlatform()) ? 131071 : 524287);
    if (isSetPlatform())
      hashCode = hashCode * 8191 + platform.getValue();

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(targetUid);

    hashCode = hashCode * 8191 + ((isSetSeqId()) ? 131071 : 524287);
    if (isSetSeqId())
      hashCode = hashCode * 8191 + seqId.hashCode();

    hashCode = hashCode * 8191 + ((isSetDescription()) ? 131071 : 524287);
    if (isSetDescription())
      hashCode = hashCode * 8191 + description.hashCode();

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(timestamp);

    hashCode = hashCode * 8191 + ((isSetExpand()) ? 131071 : 524287);
    if (isSetExpand())
      hashCode = hashCode * 8191 + expand.hashCode();

    hashCode = hashCode * 8191 + ((isSetConsumeType()) ? 131071 : 524287);
    if (isSetConsumeType())
      hashCode = hashCode * 8191 + consumeType.getValue();

    return hashCode;
  }

  @Override
  public int compareTo(TConsumeMagicBeanRequest other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.valueOf(isSetUid()).compareTo(other.isSetUid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.uid, other.uid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetAnchorUid()).compareTo(other.isSetAnchorUid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAnchorUid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.anchorUid, other.anchorUid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetSid()).compareTo(other.isSetSid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sid, other.sid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetSsid()).compareTo(other.isSetSsid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSsid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ssid, other.ssid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetCount()).compareTo(other.isSetCount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.count, other.count);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetProductId()).compareTo(other.isSetProductId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetProductId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.productId, other.productId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetPlatform()).compareTo(other.isSetPlatform());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPlatform()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.platform, other.platform);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetTargetUid()).compareTo(other.isSetTargetUid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTargetUid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.targetUid, other.targetUid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetSeqId()).compareTo(other.isSetSeqId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSeqId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.seqId, other.seqId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetDescription()).compareTo(other.isSetDescription());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDescription()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.description, other.description);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetTimestamp()).compareTo(other.isSetTimestamp());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTimestamp()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.timestamp, other.timestamp);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetExpand()).compareTo(other.isSetExpand());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExpand()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.expand, other.expand);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetConsumeType()).compareTo(other.isSetConsumeType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetConsumeType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.consumeType, other.consumeType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("TConsumeMagicBeanRequest(");
    boolean first = true;

    sb.append("uid:");
    sb.append(this.uid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("anchorUid:");
    sb.append(this.anchorUid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("sid:");
    sb.append(this.sid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("ssid:");
    sb.append(this.ssid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("count:");
    sb.append(this.count);
    first = false;
    if (!first) sb.append(", ");
    sb.append("productId:");
    if (this.productId == null) {
      sb.append("null");
    } else {
      sb.append(this.productId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("platform:");
    if (this.platform == null) {
      sb.append("null");
    } else {
      sb.append(this.platform);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("targetUid:");
    sb.append(this.targetUid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("seqId:");
    if (this.seqId == null) {
      sb.append("null");
    } else {
      sb.append(this.seqId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("description:");
    if (this.description == null) {
      sb.append("null");
    } else {
      sb.append(this.description);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("timestamp:");
    sb.append(this.timestamp);
    first = false;
    if (!first) sb.append(", ");
    sb.append("expand:");
    if (this.expand == null) {
      sb.append("null");
    } else {
      sb.append(this.expand);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("consumeType:");
    if (this.consumeType == null) {
      sb.append("null");
    } else {
      sb.append(this.consumeType);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class TConsumeMagicBeanRequestStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public TConsumeMagicBeanRequestStandardScheme getScheme() {
      return new TConsumeMagicBeanRequestStandardScheme();
    }
  }

  private static class TConsumeMagicBeanRequestStandardScheme extends org.apache.thrift.scheme.StandardScheme<TConsumeMagicBeanRequest> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, TConsumeMagicBeanRequest struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // UID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.uid = iprot.readI64();
              struct.setUidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // ANCHOR_UID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.anchorUid = iprot.readI64();
              struct.setAnchorUidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // SID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.sid = iprot.readI64();
              struct.setSidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // SSID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.ssid = iprot.readI64();
              struct.setSsidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // COUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.count = iprot.readI64();
              struct.setCountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // PRODUCT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.productId = com.yy.hd.api.thrift.fts.currency.ProductType.findByValue(iprot.readI32());
              struct.setProductIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // PLATFORM
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.platform = com.yy.hd.api.thrift.fts.currency.TPlatform.findByValue(iprot.readI32());
              struct.setPlatformIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // TARGET_UID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.targetUid = iprot.readI64();
              struct.setTargetUidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // SEQ_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.seqId = iprot.readString();
              struct.setSeqIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // DESCRIPTION
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.description = iprot.readString();
              struct.setDescriptionIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // TIMESTAMP
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.timestamp = iprot.readI64();
              struct.setTimestampIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 12: // EXPAND
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.expand = iprot.readString();
              struct.setExpandIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 13: // CONSUME_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.consumeType = com.yy.hd.api.thrift.fts.currency.MagicBeanConsumeType.findByValue(iprot.readI32());
              struct.setConsumeTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, TConsumeMagicBeanRequest struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(UID_FIELD_DESC);
      oprot.writeI64(struct.uid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ANCHOR_UID_FIELD_DESC);
      oprot.writeI64(struct.anchorUid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SID_FIELD_DESC);
      oprot.writeI64(struct.sid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SSID_FIELD_DESC);
      oprot.writeI64(struct.ssid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(COUNT_FIELD_DESC);
      oprot.writeI64(struct.count);
      oprot.writeFieldEnd();
      if (struct.productId != null) {
        oprot.writeFieldBegin(PRODUCT_ID_FIELD_DESC);
        oprot.writeI32(struct.productId.getValue());
        oprot.writeFieldEnd();
      }
      if (struct.platform != null) {
        oprot.writeFieldBegin(PLATFORM_FIELD_DESC);
        oprot.writeI32(struct.platform.getValue());
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(TARGET_UID_FIELD_DESC);
      oprot.writeI64(struct.targetUid);
      oprot.writeFieldEnd();
      if (struct.seqId != null) {
        oprot.writeFieldBegin(SEQ_ID_FIELD_DESC);
        oprot.writeString(struct.seqId);
        oprot.writeFieldEnd();
      }
      if (struct.description != null) {
        oprot.writeFieldBegin(DESCRIPTION_FIELD_DESC);
        oprot.writeString(struct.description);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(TIMESTAMP_FIELD_DESC);
      oprot.writeI64(struct.timestamp);
      oprot.writeFieldEnd();
      if (struct.expand != null) {
        oprot.writeFieldBegin(EXPAND_FIELD_DESC);
        oprot.writeString(struct.expand);
        oprot.writeFieldEnd();
      }
      if (struct.consumeType != null) {
        oprot.writeFieldBegin(CONSUME_TYPE_FIELD_DESC);
        oprot.writeI32(struct.consumeType.getValue());
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class TConsumeMagicBeanRequestTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public TConsumeMagicBeanRequestTupleScheme getScheme() {
      return new TConsumeMagicBeanRequestTupleScheme();
    }
  }

  private static class TConsumeMagicBeanRequestTupleScheme extends org.apache.thrift.scheme.TupleScheme<TConsumeMagicBeanRequest> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, TConsumeMagicBeanRequest struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetUid()) {
        optionals.set(0);
      }
      if (struct.isSetAnchorUid()) {
        optionals.set(1);
      }
      if (struct.isSetSid()) {
        optionals.set(2);
      }
      if (struct.isSetSsid()) {
        optionals.set(3);
      }
      if (struct.isSetCount()) {
        optionals.set(4);
      }
      if (struct.isSetProductId()) {
        optionals.set(5);
      }
      if (struct.isSetPlatform()) {
        optionals.set(6);
      }
      if (struct.isSetTargetUid()) {
        optionals.set(7);
      }
      if (struct.isSetSeqId()) {
        optionals.set(8);
      }
      if (struct.isSetDescription()) {
        optionals.set(9);
      }
      if (struct.isSetTimestamp()) {
        optionals.set(10);
      }
      if (struct.isSetExpand()) {
        optionals.set(11);
      }
      if (struct.isSetConsumeType()) {
        optionals.set(12);
      }
      oprot.writeBitSet(optionals, 13);
      if (struct.isSetUid()) {
        oprot.writeI64(struct.uid);
      }
      if (struct.isSetAnchorUid()) {
        oprot.writeI64(struct.anchorUid);
      }
      if (struct.isSetSid()) {
        oprot.writeI64(struct.sid);
      }
      if (struct.isSetSsid()) {
        oprot.writeI64(struct.ssid);
      }
      if (struct.isSetCount()) {
        oprot.writeI64(struct.count);
      }
      if (struct.isSetProductId()) {
        oprot.writeI32(struct.productId.getValue());
      }
      if (struct.isSetPlatform()) {
        oprot.writeI32(struct.platform.getValue());
      }
      if (struct.isSetTargetUid()) {
        oprot.writeI64(struct.targetUid);
      }
      if (struct.isSetSeqId()) {
        oprot.writeString(struct.seqId);
      }
      if (struct.isSetDescription()) {
        oprot.writeString(struct.description);
      }
      if (struct.isSetTimestamp()) {
        oprot.writeI64(struct.timestamp);
      }
      if (struct.isSetExpand()) {
        oprot.writeString(struct.expand);
      }
      if (struct.isSetConsumeType()) {
        oprot.writeI32(struct.consumeType.getValue());
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, TConsumeMagicBeanRequest struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(13);
      if (incoming.get(0)) {
        struct.uid = iprot.readI64();
        struct.setUidIsSet(true);
      }
      if (incoming.get(1)) {
        struct.anchorUid = iprot.readI64();
        struct.setAnchorUidIsSet(true);
      }
      if (incoming.get(2)) {
        struct.sid = iprot.readI64();
        struct.setSidIsSet(true);
      }
      if (incoming.get(3)) {
        struct.ssid = iprot.readI64();
        struct.setSsidIsSet(true);
      }
      if (incoming.get(4)) {
        struct.count = iprot.readI64();
        struct.setCountIsSet(true);
      }
      if (incoming.get(5)) {
        struct.productId = com.yy.hd.api.thrift.fts.currency.ProductType.findByValue(iprot.readI32());
        struct.setProductIdIsSet(true);
      }
      if (incoming.get(6)) {
        struct.platform = com.yy.hd.api.thrift.fts.currency.TPlatform.findByValue(iprot.readI32());
        struct.setPlatformIsSet(true);
      }
      if (incoming.get(7)) {
        struct.targetUid = iprot.readI64();
        struct.setTargetUidIsSet(true);
      }
      if (incoming.get(8)) {
        struct.seqId = iprot.readString();
        struct.setSeqIdIsSet(true);
      }
      if (incoming.get(9)) {
        struct.description = iprot.readString();
        struct.setDescriptionIsSet(true);
      }
      if (incoming.get(10)) {
        struct.timestamp = iprot.readI64();
        struct.setTimestampIsSet(true);
      }
      if (incoming.get(11)) {
        struct.expand = iprot.readString();
        struct.setExpandIsSet(true);
      }
      if (incoming.get(12)) {
        struct.consumeType = com.yy.hd.api.thrift.fts.currency.MagicBeanConsumeType.findByValue(iprot.readI32());
        struct.setConsumeTypeIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}


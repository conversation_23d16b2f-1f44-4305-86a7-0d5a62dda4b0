/**
 * Autogenerated by Thrift Compiler (0.11.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.hd.api.thrift.fts.currency;


public enum MagicBeanConsumeType implements org.apache.thrift.TEnum {
  Mixed(0),
  Normal(1),
  Active(2);

  private final int value;

  private MagicBeanConsumeType(int value) {
    this.value = value;
  }

  /**
   * Get the integer value of this enum value, as defined in the Thrift IDL.
   */
  public int getValue() {
    return value;
  }

  /**
   * Find a the enum type by its integer value, as defined in the Thrift IDL.
   * @return null if the value is not found.
   */
  public static MagicBeanConsumeType findByValue(int value) { 
    switch (value) {
      case 0:
        return Mixed;
      case 1:
        return Normal;
      case 2:
        return Active;
      default:
        return null;
    }
  }
}

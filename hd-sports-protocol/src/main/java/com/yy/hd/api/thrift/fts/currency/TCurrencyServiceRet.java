/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.11.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.hd.api.thrift.fts.currency;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.11.0)", date = "2025-06-27")
public class TCurrencyServiceRet implements org.apache.thrift.TBase<TCurrencyServiceRet, TCurrencyServiceRet._Fields>, java.io.Serializable, Cloneable, Comparable<TCurrencyServiceRet> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("TCurrencyServiceRet");

  private static final org.apache.thrift.protocol.TField CODE_FIELD_DESC = new org.apache.thrift.protocol.TField("code", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField MESSAGE_FIELD_DESC = new org.apache.thrift.protocol.TField("message", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField BALANCE_FIELD_DESC = new org.apache.thrift.protocol.TField("balance", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField IS_FIRST_CONSUME_FIELD_DESC = new org.apache.thrift.protocol.TField("is_first_consume", org.apache.thrift.protocol.TType.BOOL, (short)4);
  private static final org.apache.thrift.protocol.TField MAGIC_BEAN_LIST_FIELD_DESC = new org.apache.thrift.protocol.TField("magic_bean_list", org.apache.thrift.protocol.TType.LIST, (short)5);
  private static final org.apache.thrift.protocol.TField TIMESTAMP_FIELD_DESC = new org.apache.thrift.protocol.TField("timestamp", org.apache.thrift.protocol.TType.I64, (short)6);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new TCurrencyServiceRetStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new TCurrencyServiceRetTupleSchemeFactory();

  /**
   * 
   * @see TErrorCode
   */
  public TErrorCode code; // required
  public java.lang.String message; // required
  public long balance; // required
  public boolean is_first_consume; // required
  public java.util.List<MagicBeanInfo> magic_bean_list; // required
  public long timestamp; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 
     * @see TErrorCode
     */
    CODE((short)1, "code"),
    MESSAGE((short)2, "message"),
    BALANCE((short)3, "balance"),
    IS_FIRST_CONSUME((short)4, "is_first_consume"),
    MAGIC_BEAN_LIST((short)5, "magic_bean_list"),
    TIMESTAMP((short)6, "timestamp");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // CODE
          return CODE;
        case 2: // MESSAGE
          return MESSAGE;
        case 3: // BALANCE
          return BALANCE;
        case 4: // IS_FIRST_CONSUME
          return IS_FIRST_CONSUME;
        case 5: // MAGIC_BEAN_LIST
          return MAGIC_BEAN_LIST;
        case 6: // TIMESTAMP
          return TIMESTAMP;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __BALANCE_ISSET_ID = 0;
  private static final int __IS_FIRST_CONSUME_ISSET_ID = 1;
  private static final int __TIMESTAMP_ISSET_ID = 2;
  private byte __isset_bitfield = 0;
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.CODE, new org.apache.thrift.meta_data.FieldMetaData("code", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, TErrorCode.class)));
    tmpMap.put(_Fields.MESSAGE, new org.apache.thrift.meta_data.FieldMetaData("message", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.BALANCE, new org.apache.thrift.meta_data.FieldMetaData("balance", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.IS_FIRST_CONSUME, new org.apache.thrift.meta_data.FieldMetaData("is_first_consume", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    tmpMap.put(_Fields.MAGIC_BEAN_LIST, new org.apache.thrift.meta_data.FieldMetaData("magic_bean_list", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, MagicBeanInfo.class))));
    tmpMap.put(_Fields.TIMESTAMP, new org.apache.thrift.meta_data.FieldMetaData("timestamp", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(TCurrencyServiceRet.class, metaDataMap);
  }

  public TCurrencyServiceRet() {
  }

  public TCurrencyServiceRet(
    TErrorCode code,
    java.lang.String message,
    long balance,
    boolean is_first_consume,
    java.util.List<MagicBeanInfo> magic_bean_list,
    long timestamp)
  {
    this();
    this.code = code;
    this.message = message;
    this.balance = balance;
    setBalanceIsSet(true);
    this.is_first_consume = is_first_consume;
    setIs_first_consumeIsSet(true);
    this.magic_bean_list = magic_bean_list;
    this.timestamp = timestamp;
    setTimestampIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public TCurrencyServiceRet(TCurrencyServiceRet other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetCode()) {
      this.code = other.code;
    }
    if (other.isSetMessage()) {
      this.message = other.message;
    }
    this.balance = other.balance;
    this.is_first_consume = other.is_first_consume;
    if (other.isSetMagic_bean_list()) {
      java.util.List<MagicBeanInfo> __this__magic_bean_list = new java.util.ArrayList<MagicBeanInfo>(other.magic_bean_list.size());
      for (MagicBeanInfo other_element : other.magic_bean_list) {
        __this__magic_bean_list.add(new MagicBeanInfo(other_element));
      }
      this.magic_bean_list = __this__magic_bean_list;
    }
    this.timestamp = other.timestamp;
  }

  public TCurrencyServiceRet deepCopy() {
    return new TCurrencyServiceRet(this);
  }

  @Override
  public void clear() {
    this.code = null;
    this.message = null;
    setBalanceIsSet(false);
    this.balance = 0;
    setIs_first_consumeIsSet(false);
    this.is_first_consume = false;
    this.magic_bean_list = null;
    setTimestampIsSet(false);
    this.timestamp = 0;
  }

  /**
   * 
   * @see TErrorCode
   */
  public TErrorCode getCode() {
    return this.code;
  }

  /**
   * 
   * @see TErrorCode
   */
  public TCurrencyServiceRet setCode(TErrorCode code) {
    this.code = code;
    return this;
  }

  public void unsetCode() {
    this.code = null;
  }

  /** Returns true if field code is set (has been assigned a value) and false otherwise */
  public boolean isSetCode() {
    return this.code != null;
  }

  public void setCodeIsSet(boolean value) {
    if (!value) {
      this.code = null;
    }
  }

  public java.lang.String getMessage() {
    return this.message;
  }

  public TCurrencyServiceRet setMessage(java.lang.String message) {
    this.message = message;
    return this;
  }

  public void unsetMessage() {
    this.message = null;
  }

  /** Returns true if field message is set (has been assigned a value) and false otherwise */
  public boolean isSetMessage() {
    return this.message != null;
  }

  public void setMessageIsSet(boolean value) {
    if (!value) {
      this.message = null;
    }
  }

  public long getBalance() {
    return this.balance;
  }

  public TCurrencyServiceRet setBalance(long balance) {
    this.balance = balance;
    setBalanceIsSet(true);
    return this;
  }

  public void unsetBalance() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __BALANCE_ISSET_ID);
  }

  /** Returns true if field balance is set (has been assigned a value) and false otherwise */
  public boolean isSetBalance() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __BALANCE_ISSET_ID);
  }

  public void setBalanceIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __BALANCE_ISSET_ID, value);
  }

  public boolean isIs_first_consume() {
    return this.is_first_consume;
  }

  public TCurrencyServiceRet setIs_first_consume(boolean is_first_consume) {
    this.is_first_consume = is_first_consume;
    setIs_first_consumeIsSet(true);
    return this;
  }

  public void unsetIs_first_consume() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __IS_FIRST_CONSUME_ISSET_ID);
  }

  /** Returns true if field is_first_consume is set (has been assigned a value) and false otherwise */
  public boolean isSetIs_first_consume() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __IS_FIRST_CONSUME_ISSET_ID);
  }

  public void setIs_first_consumeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __IS_FIRST_CONSUME_ISSET_ID, value);
  }

  public int getMagic_bean_listSize() {
    return (this.magic_bean_list == null) ? 0 : this.magic_bean_list.size();
  }

  public java.util.Iterator<MagicBeanInfo> getMagic_bean_listIterator() {
    return (this.magic_bean_list == null) ? null : this.magic_bean_list.iterator();
  }

  public void addToMagic_bean_list(MagicBeanInfo elem) {
    if (this.magic_bean_list == null) {
      this.magic_bean_list = new java.util.ArrayList<MagicBeanInfo>();
    }
    this.magic_bean_list.add(elem);
  }

  public java.util.List<MagicBeanInfo> getMagic_bean_list() {
    return this.magic_bean_list;
  }

  public TCurrencyServiceRet setMagic_bean_list(java.util.List<MagicBeanInfo> magic_bean_list) {
    this.magic_bean_list = magic_bean_list;
    return this;
  }

  public void unsetMagic_bean_list() {
    this.magic_bean_list = null;
  }

  /** Returns true if field magic_bean_list is set (has been assigned a value) and false otherwise */
  public boolean isSetMagic_bean_list() {
    return this.magic_bean_list != null;
  }

  public void setMagic_bean_listIsSet(boolean value) {
    if (!value) {
      this.magic_bean_list = null;
    }
  }

  public long getTimestamp() {
    return this.timestamp;
  }

  public TCurrencyServiceRet setTimestamp(long timestamp) {
    this.timestamp = timestamp;
    setTimestampIsSet(true);
    return this;
  }

  public void unsetTimestamp() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __TIMESTAMP_ISSET_ID);
  }

  /** Returns true if field timestamp is set (has been assigned a value) and false otherwise */
  public boolean isSetTimestamp() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __TIMESTAMP_ISSET_ID);
  }

  public void setTimestampIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __TIMESTAMP_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, java.lang.Object value) {
    switch (field) {
    case CODE:
      if (value == null) {
        unsetCode();
      } else {
        setCode((TErrorCode)value);
      }
      break;

    case MESSAGE:
      if (value == null) {
        unsetMessage();
      } else {
        setMessage((java.lang.String)value);
      }
      break;

    case BALANCE:
      if (value == null) {
        unsetBalance();
      } else {
        setBalance((java.lang.Long)value);
      }
      break;

    case IS_FIRST_CONSUME:
      if (value == null) {
        unsetIs_first_consume();
      } else {
        setIs_first_consume((java.lang.Boolean)value);
      }
      break;

    case MAGIC_BEAN_LIST:
      if (value == null) {
        unsetMagic_bean_list();
      } else {
        setMagic_bean_list((java.util.List<MagicBeanInfo>)value);
      }
      break;

    case TIMESTAMP:
      if (value == null) {
        unsetTimestamp();
      } else {
        setTimestamp((java.lang.Long)value);
      }
      break;

    }
  }

  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case CODE:
      return getCode();

    case MESSAGE:
      return getMessage();

    case BALANCE:
      return getBalance();

    case IS_FIRST_CONSUME:
      return isIs_first_consume();

    case MAGIC_BEAN_LIST:
      return getMagic_bean_list();

    case TIMESTAMP:
      return getTimestamp();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case CODE:
      return isSetCode();
    case MESSAGE:
      return isSetMessage();
    case BALANCE:
      return isSetBalance();
    case IS_FIRST_CONSUME:
      return isSetIs_first_consume();
    case MAGIC_BEAN_LIST:
      return isSetMagic_bean_list();
    case TIMESTAMP:
      return isSetTimestamp();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that == null)
      return false;
    if (that instanceof TCurrencyServiceRet)
      return this.equals((TCurrencyServiceRet)that);
    return false;
  }

  public boolean equals(TCurrencyServiceRet that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_code = true && this.isSetCode();
    boolean that_present_code = true && that.isSetCode();
    if (this_present_code || that_present_code) {
      if (!(this_present_code && that_present_code))
        return false;
      if (!this.code.equals(that.code))
        return false;
    }

    boolean this_present_message = true && this.isSetMessage();
    boolean that_present_message = true && that.isSetMessage();
    if (this_present_message || that_present_message) {
      if (!(this_present_message && that_present_message))
        return false;
      if (!this.message.equals(that.message))
        return false;
    }

    boolean this_present_balance = true;
    boolean that_present_balance = true;
    if (this_present_balance || that_present_balance) {
      if (!(this_present_balance && that_present_balance))
        return false;
      if (this.balance != that.balance)
        return false;
    }

    boolean this_present_is_first_consume = true;
    boolean that_present_is_first_consume = true;
    if (this_present_is_first_consume || that_present_is_first_consume) {
      if (!(this_present_is_first_consume && that_present_is_first_consume))
        return false;
      if (this.is_first_consume != that.is_first_consume)
        return false;
    }

    boolean this_present_magic_bean_list = true && this.isSetMagic_bean_list();
    boolean that_present_magic_bean_list = true && that.isSetMagic_bean_list();
    if (this_present_magic_bean_list || that_present_magic_bean_list) {
      if (!(this_present_magic_bean_list && that_present_magic_bean_list))
        return false;
      if (!this.magic_bean_list.equals(that.magic_bean_list))
        return false;
    }

    boolean this_present_timestamp = true;
    boolean that_present_timestamp = true;
    if (this_present_timestamp || that_present_timestamp) {
      if (!(this_present_timestamp && that_present_timestamp))
        return false;
      if (this.timestamp != that.timestamp)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetCode()) ? 131071 : 524287);
    if (isSetCode())
      hashCode = hashCode * 8191 + code.getValue();

    hashCode = hashCode * 8191 + ((isSetMessage()) ? 131071 : 524287);
    if (isSetMessage())
      hashCode = hashCode * 8191 + message.hashCode();

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(balance);

    hashCode = hashCode * 8191 + ((is_first_consume) ? 131071 : 524287);

    hashCode = hashCode * 8191 + ((isSetMagic_bean_list()) ? 131071 : 524287);
    if (isSetMagic_bean_list())
      hashCode = hashCode * 8191 + magic_bean_list.hashCode();

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(timestamp);

    return hashCode;
  }

  @Override
  public int compareTo(TCurrencyServiceRet other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.valueOf(isSetCode()).compareTo(other.isSetCode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.code, other.code);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetMessage()).compareTo(other.isSetMessage());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMessage()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.message, other.message);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetBalance()).compareTo(other.isSetBalance());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBalance()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.balance, other.balance);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetIs_first_consume()).compareTo(other.isSetIs_first_consume());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIs_first_consume()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.is_first_consume, other.is_first_consume);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetMagic_bean_list()).compareTo(other.isSetMagic_bean_list());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMagic_bean_list()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.magic_bean_list, other.magic_bean_list);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetTimestamp()).compareTo(other.isSetTimestamp());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTimestamp()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.timestamp, other.timestamp);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("TCurrencyServiceRet(");
    boolean first = true;

    sb.append("code:");
    if (this.code == null) {
      sb.append("null");
    } else {
      sb.append(this.code);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("message:");
    if (this.message == null) {
      sb.append("null");
    } else {
      sb.append(this.message);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("balance:");
    sb.append(this.balance);
    first = false;
    if (!first) sb.append(", ");
    sb.append("is_first_consume:");
    sb.append(this.is_first_consume);
    first = false;
    if (!first) sb.append(", ");
    sb.append("magic_bean_list:");
    if (this.magic_bean_list == null) {
      sb.append("null");
    } else {
      sb.append(this.magic_bean_list);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("timestamp:");
    sb.append(this.timestamp);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class TCurrencyServiceRetStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public TCurrencyServiceRetStandardScheme getScheme() {
      return new TCurrencyServiceRetStandardScheme();
    }
  }

  private static class TCurrencyServiceRetStandardScheme extends org.apache.thrift.scheme.StandardScheme<TCurrencyServiceRet> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, TCurrencyServiceRet struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // CODE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.code = com.yy.hd.api.thrift.fts.currency.TErrorCode.findByValue(iprot.readI32());
              struct.setCodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // MESSAGE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.message = iprot.readString();
              struct.setMessageIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // BALANCE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.balance = iprot.readI64();
              struct.setBalanceIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // IS_FIRST_CONSUME
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.is_first_consume = iprot.readBool();
              struct.setIs_first_consumeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // MAGIC_BEAN_LIST
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.magic_bean_list = new java.util.ArrayList<MagicBeanInfo>(_list0.size);
                MagicBeanInfo _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new MagicBeanInfo();
                  _elem1.read(iprot);
                  struct.magic_bean_list.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setMagic_bean_listIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // TIMESTAMP
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.timestamp = iprot.readI64();
              struct.setTimestampIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, TCurrencyServiceRet struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.code != null) {
        oprot.writeFieldBegin(CODE_FIELD_DESC);
        oprot.writeI32(struct.code.getValue());
        oprot.writeFieldEnd();
      }
      if (struct.message != null) {
        oprot.writeFieldBegin(MESSAGE_FIELD_DESC);
        oprot.writeString(struct.message);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(BALANCE_FIELD_DESC);
      oprot.writeI64(struct.balance);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(IS_FIRST_CONSUME_FIELD_DESC);
      oprot.writeBool(struct.is_first_consume);
      oprot.writeFieldEnd();
      if (struct.magic_bean_list != null) {
        oprot.writeFieldBegin(MAGIC_BEAN_LIST_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.magic_bean_list.size()));
          for (MagicBeanInfo _iter3 : struct.magic_bean_list)
          {
            _iter3.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(TIMESTAMP_FIELD_DESC);
      oprot.writeI64(struct.timestamp);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class TCurrencyServiceRetTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public TCurrencyServiceRetTupleScheme getScheme() {
      return new TCurrencyServiceRetTupleScheme();
    }
  }

  private static class TCurrencyServiceRetTupleScheme extends org.apache.thrift.scheme.TupleScheme<TCurrencyServiceRet> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, TCurrencyServiceRet struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetCode()) {
        optionals.set(0);
      }
      if (struct.isSetMessage()) {
        optionals.set(1);
      }
      if (struct.isSetBalance()) {
        optionals.set(2);
      }
      if (struct.isSetIs_first_consume()) {
        optionals.set(3);
      }
      if (struct.isSetMagic_bean_list()) {
        optionals.set(4);
      }
      if (struct.isSetTimestamp()) {
        optionals.set(5);
      }
      oprot.writeBitSet(optionals, 6);
      if (struct.isSetCode()) {
        oprot.writeI32(struct.code.getValue());
      }
      if (struct.isSetMessage()) {
        oprot.writeString(struct.message);
      }
      if (struct.isSetBalance()) {
        oprot.writeI64(struct.balance);
      }
      if (struct.isSetIs_first_consume()) {
        oprot.writeBool(struct.is_first_consume);
      }
      if (struct.isSetMagic_bean_list()) {
        {
          oprot.writeI32(struct.magic_bean_list.size());
          for (MagicBeanInfo _iter4 : struct.magic_bean_list)
          {
            _iter4.write(oprot);
          }
        }
      }
      if (struct.isSetTimestamp()) {
        oprot.writeI64(struct.timestamp);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, TCurrencyServiceRet struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(6);
      if (incoming.get(0)) {
        struct.code = com.yy.hd.api.thrift.fts.currency.TErrorCode.findByValue(iprot.readI32());
        struct.setCodeIsSet(true);
      }
      if (incoming.get(1)) {
        struct.message = iprot.readString();
        struct.setMessageIsSet(true);
      }
      if (incoming.get(2)) {
        struct.balance = iprot.readI64();
        struct.setBalanceIsSet(true);
      }
      if (incoming.get(3)) {
        struct.is_first_consume = iprot.readBool();
        struct.setIs_first_consumeIsSet(true);
      }
      if (incoming.get(4)) {
        {
          org.apache.thrift.protocol.TList _list5 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.magic_bean_list = new java.util.ArrayList<MagicBeanInfo>(_list5.size);
          MagicBeanInfo _elem6;
          for (int _i7 = 0; _i7 < _list5.size; ++_i7)
          {
            _elem6 = new MagicBeanInfo();
            _elem6.read(iprot);
            struct.magic_bean_list.add(_elem6);
          }
        }
        struct.setMagic_bean_listIsSet(true);
      }
      if (incoming.get(5)) {
        struct.timestamp = iprot.readI64();
        struct.setTimestampIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}


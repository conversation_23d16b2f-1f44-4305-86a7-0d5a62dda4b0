/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.11.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.hd.api.thrift.fts.currency;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.11.0)", date = "2025-06-27")
public class MagicBeanStatsInfo implements org.apache.thrift.TBase<MagicBeanStatsInfo, MagicBeanStatsInfo._Fields>, java.io.Serializable, Cloneable, Comparable<MagicBeanStatsInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("MagicBeanStatsInfo");

  private static final org.apache.thrift.protocol.TField DATE_FIELD_DESC = new org.apache.thrift.protocol.TField("Date", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField ISSUED_MODOU_FIELD_DESC = new org.apache.thrift.protocol.TField("IssuedModou", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField REMAIN_MODOU_FIELD_DESC = new org.apache.thrift.protocol.TField("RemainModou", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField SEAL_REWARD_MODOU_FIELD_DESC = new org.apache.thrift.protocol.TField("SealRewardModou", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField GIFT_REWARD_MODOU_FIELD_DESC = new org.apache.thrift.protocol.TField("GiftRewardModou", org.apache.thrift.protocol.TType.I64, (short)5);
  private static final org.apache.thrift.protocol.TField LOTTERY_REWARD_MODOU_FIELD_DESC = new org.apache.thrift.protocol.TField("LotteryRewardModou", org.apache.thrift.protocol.TType.I64, (short)6);
  private static final org.apache.thrift.protocol.TField DOUJIA_REWARD_MODOU_FIELD_DESC = new org.apache.thrift.protocol.TField("DoujiaRewardModou", org.apache.thrift.protocol.TType.I64, (short)7);
  private static final org.apache.thrift.protocol.TField MAGIC_HAT_REWARD_MODOU_FIELD_DESC = new org.apache.thrift.protocol.TField("MagicHatRewardModou", org.apache.thrift.protocol.TType.I64, (short)8);
  private static final org.apache.thrift.protocol.TField REMAIN_ACT_MODOU_FIELD_DESC = new org.apache.thrift.protocol.TField("RemainActModou", org.apache.thrift.protocol.TType.I64, (short)9);
  private static final org.apache.thrift.protocol.TField CUSTOMER_REWARD_MODOU_FIELD_DESC = new org.apache.thrift.protocol.TField("CustomerRewardModou", org.apache.thrift.protocol.TType.I64, (short)10);
  private static final org.apache.thrift.protocol.TField FOUNTAIN_REWARD_MODOU_FIELD_DESC = new org.apache.thrift.protocol.TField("FountainRewardModou", org.apache.thrift.protocol.TType.I64, (short)11);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new MagicBeanStatsInfoStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new MagicBeanStatsInfoTupleSchemeFactory();

  public java.lang.String Date; // required
  public long IssuedModou; // required
  public long RemainModou; // required
  public long SealRewardModou; // required
  public long GiftRewardModou; // required
  public long LotteryRewardModou; // required
  public long DoujiaRewardModou; // required
  public long MagicHatRewardModou; // required
  public long RemainActModou; // required
  public long CustomerRewardModou; // required
  public long FountainRewardModou; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    DATE((short)1, "Date"),
    ISSUED_MODOU((short)2, "IssuedModou"),
    REMAIN_MODOU((short)3, "RemainModou"),
    SEAL_REWARD_MODOU((short)4, "SealRewardModou"),
    GIFT_REWARD_MODOU((short)5, "GiftRewardModou"),
    LOTTERY_REWARD_MODOU((short)6, "LotteryRewardModou"),
    DOUJIA_REWARD_MODOU((short)7, "DoujiaRewardModou"),
    MAGIC_HAT_REWARD_MODOU((short)8, "MagicHatRewardModou"),
    REMAIN_ACT_MODOU((short)9, "RemainActModou"),
    CUSTOMER_REWARD_MODOU((short)10, "CustomerRewardModou"),
    FOUNTAIN_REWARD_MODOU((short)11, "FountainRewardModou");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // DATE
          return DATE;
        case 2: // ISSUED_MODOU
          return ISSUED_MODOU;
        case 3: // REMAIN_MODOU
          return REMAIN_MODOU;
        case 4: // SEAL_REWARD_MODOU
          return SEAL_REWARD_MODOU;
        case 5: // GIFT_REWARD_MODOU
          return GIFT_REWARD_MODOU;
        case 6: // LOTTERY_REWARD_MODOU
          return LOTTERY_REWARD_MODOU;
        case 7: // DOUJIA_REWARD_MODOU
          return DOUJIA_REWARD_MODOU;
        case 8: // MAGIC_HAT_REWARD_MODOU
          return MAGIC_HAT_REWARD_MODOU;
        case 9: // REMAIN_ACT_MODOU
          return REMAIN_ACT_MODOU;
        case 10: // CUSTOMER_REWARD_MODOU
          return CUSTOMER_REWARD_MODOU;
        case 11: // FOUNTAIN_REWARD_MODOU
          return FOUNTAIN_REWARD_MODOU;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ISSUEDMODOU_ISSET_ID = 0;
  private static final int __REMAINMODOU_ISSET_ID = 1;
  private static final int __SEALREWARDMODOU_ISSET_ID = 2;
  private static final int __GIFTREWARDMODOU_ISSET_ID = 3;
  private static final int __LOTTERYREWARDMODOU_ISSET_ID = 4;
  private static final int __DOUJIAREWARDMODOU_ISSET_ID = 5;
  private static final int __MAGICHATREWARDMODOU_ISSET_ID = 6;
  private static final int __REMAINACTMODOU_ISSET_ID = 7;
  private static final int __CUSTOMERREWARDMODOU_ISSET_ID = 8;
  private static final int __FOUNTAINREWARDMODOU_ISSET_ID = 9;
  private short __isset_bitfield = 0;
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.DATE, new org.apache.thrift.meta_data.FieldMetaData("Date", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ISSUED_MODOU, new org.apache.thrift.meta_data.FieldMetaData("IssuedModou", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.REMAIN_MODOU, new org.apache.thrift.meta_data.FieldMetaData("RemainModou", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SEAL_REWARD_MODOU, new org.apache.thrift.meta_data.FieldMetaData("SealRewardModou", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.GIFT_REWARD_MODOU, new org.apache.thrift.meta_data.FieldMetaData("GiftRewardModou", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.LOTTERY_REWARD_MODOU, new org.apache.thrift.meta_data.FieldMetaData("LotteryRewardModou", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.DOUJIA_REWARD_MODOU, new org.apache.thrift.meta_data.FieldMetaData("DoujiaRewardModou", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.MAGIC_HAT_REWARD_MODOU, new org.apache.thrift.meta_data.FieldMetaData("MagicHatRewardModou", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.REMAIN_ACT_MODOU, new org.apache.thrift.meta_data.FieldMetaData("RemainActModou", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.CUSTOMER_REWARD_MODOU, new org.apache.thrift.meta_data.FieldMetaData("CustomerRewardModou", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.FOUNTAIN_REWARD_MODOU, new org.apache.thrift.meta_data.FieldMetaData("FountainRewardModou", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(MagicBeanStatsInfo.class, metaDataMap);
  }

  public MagicBeanStatsInfo() {
  }

  public MagicBeanStatsInfo(
    java.lang.String Date,
    long IssuedModou,
    long RemainModou,
    long SealRewardModou,
    long GiftRewardModou,
    long LotteryRewardModou,
    long DoujiaRewardModou,
    long MagicHatRewardModou,
    long RemainActModou,
    long CustomerRewardModou,
    long FountainRewardModou)
  {
    this();
    this.Date = Date;
    this.IssuedModou = IssuedModou;
    setIssuedModouIsSet(true);
    this.RemainModou = RemainModou;
    setRemainModouIsSet(true);
    this.SealRewardModou = SealRewardModou;
    setSealRewardModouIsSet(true);
    this.GiftRewardModou = GiftRewardModou;
    setGiftRewardModouIsSet(true);
    this.LotteryRewardModou = LotteryRewardModou;
    setLotteryRewardModouIsSet(true);
    this.DoujiaRewardModou = DoujiaRewardModou;
    setDoujiaRewardModouIsSet(true);
    this.MagicHatRewardModou = MagicHatRewardModou;
    setMagicHatRewardModouIsSet(true);
    this.RemainActModou = RemainActModou;
    setRemainActModouIsSet(true);
    this.CustomerRewardModou = CustomerRewardModou;
    setCustomerRewardModouIsSet(true);
    this.FountainRewardModou = FountainRewardModou;
    setFountainRewardModouIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public MagicBeanStatsInfo(MagicBeanStatsInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetDate()) {
      this.Date = other.Date;
    }
    this.IssuedModou = other.IssuedModou;
    this.RemainModou = other.RemainModou;
    this.SealRewardModou = other.SealRewardModou;
    this.GiftRewardModou = other.GiftRewardModou;
    this.LotteryRewardModou = other.LotteryRewardModou;
    this.DoujiaRewardModou = other.DoujiaRewardModou;
    this.MagicHatRewardModou = other.MagicHatRewardModou;
    this.RemainActModou = other.RemainActModou;
    this.CustomerRewardModou = other.CustomerRewardModou;
    this.FountainRewardModou = other.FountainRewardModou;
  }

  public MagicBeanStatsInfo deepCopy() {
    return new MagicBeanStatsInfo(this);
  }

  @Override
  public void clear() {
    this.Date = null;
    setIssuedModouIsSet(false);
    this.IssuedModou = 0;
    setRemainModouIsSet(false);
    this.RemainModou = 0;
    setSealRewardModouIsSet(false);
    this.SealRewardModou = 0;
    setGiftRewardModouIsSet(false);
    this.GiftRewardModou = 0;
    setLotteryRewardModouIsSet(false);
    this.LotteryRewardModou = 0;
    setDoujiaRewardModouIsSet(false);
    this.DoujiaRewardModou = 0;
    setMagicHatRewardModouIsSet(false);
    this.MagicHatRewardModou = 0;
    setRemainActModouIsSet(false);
    this.RemainActModou = 0;
    setCustomerRewardModouIsSet(false);
    this.CustomerRewardModou = 0;
    setFountainRewardModouIsSet(false);
    this.FountainRewardModou = 0;
  }

  public java.lang.String getDate() {
    return this.Date;
  }

  public MagicBeanStatsInfo setDate(java.lang.String Date) {
    this.Date = Date;
    return this;
  }

  public void unsetDate() {
    this.Date = null;
  }

  /** Returns true if field Date is set (has been assigned a value) and false otherwise */
  public boolean isSetDate() {
    return this.Date != null;
  }

  public void setDateIsSet(boolean value) {
    if (!value) {
      this.Date = null;
    }
  }

  public long getIssuedModou() {
    return this.IssuedModou;
  }

  public MagicBeanStatsInfo setIssuedModou(long IssuedModou) {
    this.IssuedModou = IssuedModou;
    setIssuedModouIsSet(true);
    return this;
  }

  public void unsetIssuedModou() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ISSUEDMODOU_ISSET_ID);
  }

  /** Returns true if field IssuedModou is set (has been assigned a value) and false otherwise */
  public boolean isSetIssuedModou() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ISSUEDMODOU_ISSET_ID);
  }

  public void setIssuedModouIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ISSUEDMODOU_ISSET_ID, value);
  }

  public long getRemainModou() {
    return this.RemainModou;
  }

  public MagicBeanStatsInfo setRemainModou(long RemainModou) {
    this.RemainModou = RemainModou;
    setRemainModouIsSet(true);
    return this;
  }

  public void unsetRemainModou() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __REMAINMODOU_ISSET_ID);
  }

  /** Returns true if field RemainModou is set (has been assigned a value) and false otherwise */
  public boolean isSetRemainModou() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __REMAINMODOU_ISSET_ID);
  }

  public void setRemainModouIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __REMAINMODOU_ISSET_ID, value);
  }

  public long getSealRewardModou() {
    return this.SealRewardModou;
  }

  public MagicBeanStatsInfo setSealRewardModou(long SealRewardModou) {
    this.SealRewardModou = SealRewardModou;
    setSealRewardModouIsSet(true);
    return this;
  }

  public void unsetSealRewardModou() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __SEALREWARDMODOU_ISSET_ID);
  }

  /** Returns true if field SealRewardModou is set (has been assigned a value) and false otherwise */
  public boolean isSetSealRewardModou() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __SEALREWARDMODOU_ISSET_ID);
  }

  public void setSealRewardModouIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __SEALREWARDMODOU_ISSET_ID, value);
  }

  public long getGiftRewardModou() {
    return this.GiftRewardModou;
  }

  public MagicBeanStatsInfo setGiftRewardModou(long GiftRewardModou) {
    this.GiftRewardModou = GiftRewardModou;
    setGiftRewardModouIsSet(true);
    return this;
  }

  public void unsetGiftRewardModou() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __GIFTREWARDMODOU_ISSET_ID);
  }

  /** Returns true if field GiftRewardModou is set (has been assigned a value) and false otherwise */
  public boolean isSetGiftRewardModou() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __GIFTREWARDMODOU_ISSET_ID);
  }

  public void setGiftRewardModouIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __GIFTREWARDMODOU_ISSET_ID, value);
  }

  public long getLotteryRewardModou() {
    return this.LotteryRewardModou;
  }

  public MagicBeanStatsInfo setLotteryRewardModou(long LotteryRewardModou) {
    this.LotteryRewardModou = LotteryRewardModou;
    setLotteryRewardModouIsSet(true);
    return this;
  }

  public void unsetLotteryRewardModou() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __LOTTERYREWARDMODOU_ISSET_ID);
  }

  /** Returns true if field LotteryRewardModou is set (has been assigned a value) and false otherwise */
  public boolean isSetLotteryRewardModou() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __LOTTERYREWARDMODOU_ISSET_ID);
  }

  public void setLotteryRewardModouIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __LOTTERYREWARDMODOU_ISSET_ID, value);
  }

  public long getDoujiaRewardModou() {
    return this.DoujiaRewardModou;
  }

  public MagicBeanStatsInfo setDoujiaRewardModou(long DoujiaRewardModou) {
    this.DoujiaRewardModou = DoujiaRewardModou;
    setDoujiaRewardModouIsSet(true);
    return this;
  }

  public void unsetDoujiaRewardModou() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __DOUJIAREWARDMODOU_ISSET_ID);
  }

  /** Returns true if field DoujiaRewardModou is set (has been assigned a value) and false otherwise */
  public boolean isSetDoujiaRewardModou() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __DOUJIAREWARDMODOU_ISSET_ID);
  }

  public void setDoujiaRewardModouIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __DOUJIAREWARDMODOU_ISSET_ID, value);
  }

  public long getMagicHatRewardModou() {
    return this.MagicHatRewardModou;
  }

  public MagicBeanStatsInfo setMagicHatRewardModou(long MagicHatRewardModou) {
    this.MagicHatRewardModou = MagicHatRewardModou;
    setMagicHatRewardModouIsSet(true);
    return this;
  }

  public void unsetMagicHatRewardModou() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __MAGICHATREWARDMODOU_ISSET_ID);
  }

  /** Returns true if field MagicHatRewardModou is set (has been assigned a value) and false otherwise */
  public boolean isSetMagicHatRewardModou() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __MAGICHATREWARDMODOU_ISSET_ID);
  }

  public void setMagicHatRewardModouIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __MAGICHATREWARDMODOU_ISSET_ID, value);
  }

  public long getRemainActModou() {
    return this.RemainActModou;
  }

  public MagicBeanStatsInfo setRemainActModou(long RemainActModou) {
    this.RemainActModou = RemainActModou;
    setRemainActModouIsSet(true);
    return this;
  }

  public void unsetRemainActModou() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __REMAINACTMODOU_ISSET_ID);
  }

  /** Returns true if field RemainActModou is set (has been assigned a value) and false otherwise */
  public boolean isSetRemainActModou() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __REMAINACTMODOU_ISSET_ID);
  }

  public void setRemainActModouIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __REMAINACTMODOU_ISSET_ID, value);
  }

  public long getCustomerRewardModou() {
    return this.CustomerRewardModou;
  }

  public MagicBeanStatsInfo setCustomerRewardModou(long CustomerRewardModou) {
    this.CustomerRewardModou = CustomerRewardModou;
    setCustomerRewardModouIsSet(true);
    return this;
  }

  public void unsetCustomerRewardModou() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __CUSTOMERREWARDMODOU_ISSET_ID);
  }

  /** Returns true if field CustomerRewardModou is set (has been assigned a value) and false otherwise */
  public boolean isSetCustomerRewardModou() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __CUSTOMERREWARDMODOU_ISSET_ID);
  }

  public void setCustomerRewardModouIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __CUSTOMERREWARDMODOU_ISSET_ID, value);
  }

  public long getFountainRewardModou() {
    return this.FountainRewardModou;
  }

  public MagicBeanStatsInfo setFountainRewardModou(long FountainRewardModou) {
    this.FountainRewardModou = FountainRewardModou;
    setFountainRewardModouIsSet(true);
    return this;
  }

  public void unsetFountainRewardModou() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __FOUNTAINREWARDMODOU_ISSET_ID);
  }

  /** Returns true if field FountainRewardModou is set (has been assigned a value) and false otherwise */
  public boolean isSetFountainRewardModou() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __FOUNTAINREWARDMODOU_ISSET_ID);
  }

  public void setFountainRewardModouIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __FOUNTAINREWARDMODOU_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, java.lang.Object value) {
    switch (field) {
    case DATE:
      if (value == null) {
        unsetDate();
      } else {
        setDate((java.lang.String)value);
      }
      break;

    case ISSUED_MODOU:
      if (value == null) {
        unsetIssuedModou();
      } else {
        setIssuedModou((java.lang.Long)value);
      }
      break;

    case REMAIN_MODOU:
      if (value == null) {
        unsetRemainModou();
      } else {
        setRemainModou((java.lang.Long)value);
      }
      break;

    case SEAL_REWARD_MODOU:
      if (value == null) {
        unsetSealRewardModou();
      } else {
        setSealRewardModou((java.lang.Long)value);
      }
      break;

    case GIFT_REWARD_MODOU:
      if (value == null) {
        unsetGiftRewardModou();
      } else {
        setGiftRewardModou((java.lang.Long)value);
      }
      break;

    case LOTTERY_REWARD_MODOU:
      if (value == null) {
        unsetLotteryRewardModou();
      } else {
        setLotteryRewardModou((java.lang.Long)value);
      }
      break;

    case DOUJIA_REWARD_MODOU:
      if (value == null) {
        unsetDoujiaRewardModou();
      } else {
        setDoujiaRewardModou((java.lang.Long)value);
      }
      break;

    case MAGIC_HAT_REWARD_MODOU:
      if (value == null) {
        unsetMagicHatRewardModou();
      } else {
        setMagicHatRewardModou((java.lang.Long)value);
      }
      break;

    case REMAIN_ACT_MODOU:
      if (value == null) {
        unsetRemainActModou();
      } else {
        setRemainActModou((java.lang.Long)value);
      }
      break;

    case CUSTOMER_REWARD_MODOU:
      if (value == null) {
        unsetCustomerRewardModou();
      } else {
        setCustomerRewardModou((java.lang.Long)value);
      }
      break;

    case FOUNTAIN_REWARD_MODOU:
      if (value == null) {
        unsetFountainRewardModou();
      } else {
        setFountainRewardModou((java.lang.Long)value);
      }
      break;

    }
  }

  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case DATE:
      return getDate();

    case ISSUED_MODOU:
      return getIssuedModou();

    case REMAIN_MODOU:
      return getRemainModou();

    case SEAL_REWARD_MODOU:
      return getSealRewardModou();

    case GIFT_REWARD_MODOU:
      return getGiftRewardModou();

    case LOTTERY_REWARD_MODOU:
      return getLotteryRewardModou();

    case DOUJIA_REWARD_MODOU:
      return getDoujiaRewardModou();

    case MAGIC_HAT_REWARD_MODOU:
      return getMagicHatRewardModou();

    case REMAIN_ACT_MODOU:
      return getRemainActModou();

    case CUSTOMER_REWARD_MODOU:
      return getCustomerRewardModou();

    case FOUNTAIN_REWARD_MODOU:
      return getFountainRewardModou();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case DATE:
      return isSetDate();
    case ISSUED_MODOU:
      return isSetIssuedModou();
    case REMAIN_MODOU:
      return isSetRemainModou();
    case SEAL_REWARD_MODOU:
      return isSetSealRewardModou();
    case GIFT_REWARD_MODOU:
      return isSetGiftRewardModou();
    case LOTTERY_REWARD_MODOU:
      return isSetLotteryRewardModou();
    case DOUJIA_REWARD_MODOU:
      return isSetDoujiaRewardModou();
    case MAGIC_HAT_REWARD_MODOU:
      return isSetMagicHatRewardModou();
    case REMAIN_ACT_MODOU:
      return isSetRemainActModou();
    case CUSTOMER_REWARD_MODOU:
      return isSetCustomerRewardModou();
    case FOUNTAIN_REWARD_MODOU:
      return isSetFountainRewardModou();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that == null)
      return false;
    if (that instanceof MagicBeanStatsInfo)
      return this.equals((MagicBeanStatsInfo)that);
    return false;
  }

  public boolean equals(MagicBeanStatsInfo that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_Date = true && this.isSetDate();
    boolean that_present_Date = true && that.isSetDate();
    if (this_present_Date || that_present_Date) {
      if (!(this_present_Date && that_present_Date))
        return false;
      if (!this.Date.equals(that.Date))
        return false;
    }

    boolean this_present_IssuedModou = true;
    boolean that_present_IssuedModou = true;
    if (this_present_IssuedModou || that_present_IssuedModou) {
      if (!(this_present_IssuedModou && that_present_IssuedModou))
        return false;
      if (this.IssuedModou != that.IssuedModou)
        return false;
    }

    boolean this_present_RemainModou = true;
    boolean that_present_RemainModou = true;
    if (this_present_RemainModou || that_present_RemainModou) {
      if (!(this_present_RemainModou && that_present_RemainModou))
        return false;
      if (this.RemainModou != that.RemainModou)
        return false;
    }

    boolean this_present_SealRewardModou = true;
    boolean that_present_SealRewardModou = true;
    if (this_present_SealRewardModou || that_present_SealRewardModou) {
      if (!(this_present_SealRewardModou && that_present_SealRewardModou))
        return false;
      if (this.SealRewardModou != that.SealRewardModou)
        return false;
    }

    boolean this_present_GiftRewardModou = true;
    boolean that_present_GiftRewardModou = true;
    if (this_present_GiftRewardModou || that_present_GiftRewardModou) {
      if (!(this_present_GiftRewardModou && that_present_GiftRewardModou))
        return false;
      if (this.GiftRewardModou != that.GiftRewardModou)
        return false;
    }

    boolean this_present_LotteryRewardModou = true;
    boolean that_present_LotteryRewardModou = true;
    if (this_present_LotteryRewardModou || that_present_LotteryRewardModou) {
      if (!(this_present_LotteryRewardModou && that_present_LotteryRewardModou))
        return false;
      if (this.LotteryRewardModou != that.LotteryRewardModou)
        return false;
    }

    boolean this_present_DoujiaRewardModou = true;
    boolean that_present_DoujiaRewardModou = true;
    if (this_present_DoujiaRewardModou || that_present_DoujiaRewardModou) {
      if (!(this_present_DoujiaRewardModou && that_present_DoujiaRewardModou))
        return false;
      if (this.DoujiaRewardModou != that.DoujiaRewardModou)
        return false;
    }

    boolean this_present_MagicHatRewardModou = true;
    boolean that_present_MagicHatRewardModou = true;
    if (this_present_MagicHatRewardModou || that_present_MagicHatRewardModou) {
      if (!(this_present_MagicHatRewardModou && that_present_MagicHatRewardModou))
        return false;
      if (this.MagicHatRewardModou != that.MagicHatRewardModou)
        return false;
    }

    boolean this_present_RemainActModou = true;
    boolean that_present_RemainActModou = true;
    if (this_present_RemainActModou || that_present_RemainActModou) {
      if (!(this_present_RemainActModou && that_present_RemainActModou))
        return false;
      if (this.RemainActModou != that.RemainActModou)
        return false;
    }

    boolean this_present_CustomerRewardModou = true;
    boolean that_present_CustomerRewardModou = true;
    if (this_present_CustomerRewardModou || that_present_CustomerRewardModou) {
      if (!(this_present_CustomerRewardModou && that_present_CustomerRewardModou))
        return false;
      if (this.CustomerRewardModou != that.CustomerRewardModou)
        return false;
    }

    boolean this_present_FountainRewardModou = true;
    boolean that_present_FountainRewardModou = true;
    if (this_present_FountainRewardModou || that_present_FountainRewardModou) {
      if (!(this_present_FountainRewardModou && that_present_FountainRewardModou))
        return false;
      if (this.FountainRewardModou != that.FountainRewardModou)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetDate()) ? 131071 : 524287);
    if (isSetDate())
      hashCode = hashCode * 8191 + Date.hashCode();

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(IssuedModou);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(RemainModou);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(SealRewardModou);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(GiftRewardModou);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(LotteryRewardModou);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(DoujiaRewardModou);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(MagicHatRewardModou);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(RemainActModou);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(CustomerRewardModou);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(FountainRewardModou);

    return hashCode;
  }

  @Override
  public int compareTo(MagicBeanStatsInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.valueOf(isSetDate()).compareTo(other.isSetDate());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDate()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.Date, other.Date);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetIssuedModou()).compareTo(other.isSetIssuedModou());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIssuedModou()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.IssuedModou, other.IssuedModou);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetRemainModou()).compareTo(other.isSetRemainModou());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRemainModou()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.RemainModou, other.RemainModou);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetSealRewardModou()).compareTo(other.isSetSealRewardModou());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSealRewardModou()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.SealRewardModou, other.SealRewardModou);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetGiftRewardModou()).compareTo(other.isSetGiftRewardModou());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGiftRewardModou()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.GiftRewardModou, other.GiftRewardModou);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetLotteryRewardModou()).compareTo(other.isSetLotteryRewardModou());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetLotteryRewardModou()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.LotteryRewardModou, other.LotteryRewardModou);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetDoujiaRewardModou()).compareTo(other.isSetDoujiaRewardModou());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDoujiaRewardModou()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.DoujiaRewardModou, other.DoujiaRewardModou);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetMagicHatRewardModou()).compareTo(other.isSetMagicHatRewardModou());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMagicHatRewardModou()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.MagicHatRewardModou, other.MagicHatRewardModou);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetRemainActModou()).compareTo(other.isSetRemainActModou());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRemainActModou()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.RemainActModou, other.RemainActModou);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetCustomerRewardModou()).compareTo(other.isSetCustomerRewardModou());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCustomerRewardModou()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.CustomerRewardModou, other.CustomerRewardModou);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetFountainRewardModou()).compareTo(other.isSetFountainRewardModou());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetFountainRewardModou()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.FountainRewardModou, other.FountainRewardModou);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("MagicBeanStatsInfo(");
    boolean first = true;

    sb.append("Date:");
    if (this.Date == null) {
      sb.append("null");
    } else {
      sb.append(this.Date);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("IssuedModou:");
    sb.append(this.IssuedModou);
    first = false;
    if (!first) sb.append(", ");
    sb.append("RemainModou:");
    sb.append(this.RemainModou);
    first = false;
    if (!first) sb.append(", ");
    sb.append("SealRewardModou:");
    sb.append(this.SealRewardModou);
    first = false;
    if (!first) sb.append(", ");
    sb.append("GiftRewardModou:");
    sb.append(this.GiftRewardModou);
    first = false;
    if (!first) sb.append(", ");
    sb.append("LotteryRewardModou:");
    sb.append(this.LotteryRewardModou);
    first = false;
    if (!first) sb.append(", ");
    sb.append("DoujiaRewardModou:");
    sb.append(this.DoujiaRewardModou);
    first = false;
    if (!first) sb.append(", ");
    sb.append("MagicHatRewardModou:");
    sb.append(this.MagicHatRewardModou);
    first = false;
    if (!first) sb.append(", ");
    sb.append("RemainActModou:");
    sb.append(this.RemainActModou);
    first = false;
    if (!first) sb.append(", ");
    sb.append("CustomerRewardModou:");
    sb.append(this.CustomerRewardModou);
    first = false;
    if (!first) sb.append(", ");
    sb.append("FountainRewardModou:");
    sb.append(this.FountainRewardModou);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class MagicBeanStatsInfoStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public MagicBeanStatsInfoStandardScheme getScheme() {
      return new MagicBeanStatsInfoStandardScheme();
    }
  }

  private static class MagicBeanStatsInfoStandardScheme extends org.apache.thrift.scheme.StandardScheme<MagicBeanStatsInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, MagicBeanStatsInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // DATE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.Date = iprot.readString();
              struct.setDateIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // ISSUED_MODOU
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.IssuedModou = iprot.readI64();
              struct.setIssuedModouIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // REMAIN_MODOU
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.RemainModou = iprot.readI64();
              struct.setRemainModouIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // SEAL_REWARD_MODOU
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.SealRewardModou = iprot.readI64();
              struct.setSealRewardModouIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // GIFT_REWARD_MODOU
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.GiftRewardModou = iprot.readI64();
              struct.setGiftRewardModouIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // LOTTERY_REWARD_MODOU
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.LotteryRewardModou = iprot.readI64();
              struct.setLotteryRewardModouIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // DOUJIA_REWARD_MODOU
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.DoujiaRewardModou = iprot.readI64();
              struct.setDoujiaRewardModouIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // MAGIC_HAT_REWARD_MODOU
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.MagicHatRewardModou = iprot.readI64();
              struct.setMagicHatRewardModouIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // REMAIN_ACT_MODOU
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.RemainActModou = iprot.readI64();
              struct.setRemainActModouIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // CUSTOMER_REWARD_MODOU
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.CustomerRewardModou = iprot.readI64();
              struct.setCustomerRewardModouIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // FOUNTAIN_REWARD_MODOU
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.FountainRewardModou = iprot.readI64();
              struct.setFountainRewardModouIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, MagicBeanStatsInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.Date != null) {
        oprot.writeFieldBegin(DATE_FIELD_DESC);
        oprot.writeString(struct.Date);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(ISSUED_MODOU_FIELD_DESC);
      oprot.writeI64(struct.IssuedModou);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(REMAIN_MODOU_FIELD_DESC);
      oprot.writeI64(struct.RemainModou);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SEAL_REWARD_MODOU_FIELD_DESC);
      oprot.writeI64(struct.SealRewardModou);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(GIFT_REWARD_MODOU_FIELD_DESC);
      oprot.writeI64(struct.GiftRewardModou);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(LOTTERY_REWARD_MODOU_FIELD_DESC);
      oprot.writeI64(struct.LotteryRewardModou);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(DOUJIA_REWARD_MODOU_FIELD_DESC);
      oprot.writeI64(struct.DoujiaRewardModou);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(MAGIC_HAT_REWARD_MODOU_FIELD_DESC);
      oprot.writeI64(struct.MagicHatRewardModou);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(REMAIN_ACT_MODOU_FIELD_DESC);
      oprot.writeI64(struct.RemainActModou);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(CUSTOMER_REWARD_MODOU_FIELD_DESC);
      oprot.writeI64(struct.CustomerRewardModou);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(FOUNTAIN_REWARD_MODOU_FIELD_DESC);
      oprot.writeI64(struct.FountainRewardModou);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class MagicBeanStatsInfoTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public MagicBeanStatsInfoTupleScheme getScheme() {
      return new MagicBeanStatsInfoTupleScheme();
    }
  }

  private static class MagicBeanStatsInfoTupleScheme extends org.apache.thrift.scheme.TupleScheme<MagicBeanStatsInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, MagicBeanStatsInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetDate()) {
        optionals.set(0);
      }
      if (struct.isSetIssuedModou()) {
        optionals.set(1);
      }
      if (struct.isSetRemainModou()) {
        optionals.set(2);
      }
      if (struct.isSetSealRewardModou()) {
        optionals.set(3);
      }
      if (struct.isSetGiftRewardModou()) {
        optionals.set(4);
      }
      if (struct.isSetLotteryRewardModou()) {
        optionals.set(5);
      }
      if (struct.isSetDoujiaRewardModou()) {
        optionals.set(6);
      }
      if (struct.isSetMagicHatRewardModou()) {
        optionals.set(7);
      }
      if (struct.isSetRemainActModou()) {
        optionals.set(8);
      }
      if (struct.isSetCustomerRewardModou()) {
        optionals.set(9);
      }
      if (struct.isSetFountainRewardModou()) {
        optionals.set(10);
      }
      oprot.writeBitSet(optionals, 11);
      if (struct.isSetDate()) {
        oprot.writeString(struct.Date);
      }
      if (struct.isSetIssuedModou()) {
        oprot.writeI64(struct.IssuedModou);
      }
      if (struct.isSetRemainModou()) {
        oprot.writeI64(struct.RemainModou);
      }
      if (struct.isSetSealRewardModou()) {
        oprot.writeI64(struct.SealRewardModou);
      }
      if (struct.isSetGiftRewardModou()) {
        oprot.writeI64(struct.GiftRewardModou);
      }
      if (struct.isSetLotteryRewardModou()) {
        oprot.writeI64(struct.LotteryRewardModou);
      }
      if (struct.isSetDoujiaRewardModou()) {
        oprot.writeI64(struct.DoujiaRewardModou);
      }
      if (struct.isSetMagicHatRewardModou()) {
        oprot.writeI64(struct.MagicHatRewardModou);
      }
      if (struct.isSetRemainActModou()) {
        oprot.writeI64(struct.RemainActModou);
      }
      if (struct.isSetCustomerRewardModou()) {
        oprot.writeI64(struct.CustomerRewardModou);
      }
      if (struct.isSetFountainRewardModou()) {
        oprot.writeI64(struct.FountainRewardModou);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, MagicBeanStatsInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(11);
      if (incoming.get(0)) {
        struct.Date = iprot.readString();
        struct.setDateIsSet(true);
      }
      if (incoming.get(1)) {
        struct.IssuedModou = iprot.readI64();
        struct.setIssuedModouIsSet(true);
      }
      if (incoming.get(2)) {
        struct.RemainModou = iprot.readI64();
        struct.setRemainModouIsSet(true);
      }
      if (incoming.get(3)) {
        struct.SealRewardModou = iprot.readI64();
        struct.setSealRewardModouIsSet(true);
      }
      if (incoming.get(4)) {
        struct.GiftRewardModou = iprot.readI64();
        struct.setGiftRewardModouIsSet(true);
      }
      if (incoming.get(5)) {
        struct.LotteryRewardModou = iprot.readI64();
        struct.setLotteryRewardModouIsSet(true);
      }
      if (incoming.get(6)) {
        struct.DoujiaRewardModou = iprot.readI64();
        struct.setDoujiaRewardModouIsSet(true);
      }
      if (incoming.get(7)) {
        struct.MagicHatRewardModou = iprot.readI64();
        struct.setMagicHatRewardModouIsSet(true);
      }
      if (incoming.get(8)) {
        struct.RemainActModou = iprot.readI64();
        struct.setRemainActModouIsSet(true);
      }
      if (incoming.get(9)) {
        struct.CustomerRewardModou = iprot.readI64();
        struct.setCustomerRewardModouIsSet(true);
      }
      if (incoming.get(10)) {
        struct.FountainRewardModou = iprot.readI64();
        struct.setFountainRewardModouIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}


/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.11.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.hd.api.thrift.fts.currency;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.11.0)", date = "2025-06-27")
public class TMagicBeanIssueCountRet implements org.apache.thrift.TBase<TMagicBeanIssueCountRet, TMagicBeanIssueCountRet._Fields>, java.io.Serializable, Cloneable, Comparable<TMagicBeanIssueCountRet> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("TMagicBeanIssueCountRet");

  private static final org.apache.thrift.protocol.TField CODE_FIELD_DESC = new org.apache.thrift.protocol.TField("code", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField MESSAGE_FIELD_DESC = new org.apache.thrift.protocol.TField("message", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField ISSUE_STAT_FIELD_DESC = new org.apache.thrift.protocol.TField("issue_stat", org.apache.thrift.protocol.TType.MAP, (short)3);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new TMagicBeanIssueCountRetStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new TMagicBeanIssueCountRetTupleSchemeFactory();

  /**
   * 
   * @see TErrorCode
   */
  public TErrorCode code; // required
  public java.lang.String message; // required
  public java.util.Map<java.lang.Long,java.lang.Long> issue_stat; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 
     * @see TErrorCode
     */
    CODE((short)1, "code"),
    MESSAGE((short)2, "message"),
    ISSUE_STAT((short)3, "issue_stat");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // CODE
          return CODE;
        case 2: // MESSAGE
          return MESSAGE;
        case 3: // ISSUE_STAT
          return ISSUE_STAT;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.CODE, new org.apache.thrift.meta_data.FieldMetaData("code", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, TErrorCode.class)));
    tmpMap.put(_Fields.MESSAGE, new org.apache.thrift.meta_data.FieldMetaData("message", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ISSUE_STAT, new org.apache.thrift.meta_data.FieldMetaData("issue_stat", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(TMagicBeanIssueCountRet.class, metaDataMap);
  }

  public TMagicBeanIssueCountRet() {
  }

  public TMagicBeanIssueCountRet(
    TErrorCode code,
    java.lang.String message,
    java.util.Map<java.lang.Long,java.lang.Long> issue_stat)
  {
    this();
    this.code = code;
    this.message = message;
    this.issue_stat = issue_stat;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public TMagicBeanIssueCountRet(TMagicBeanIssueCountRet other) {
    if (other.isSetCode()) {
      this.code = other.code;
    }
    if (other.isSetMessage()) {
      this.message = other.message;
    }
    if (other.isSetIssue_stat()) {
      java.util.Map<java.lang.Long,java.lang.Long> __this__issue_stat = new java.util.HashMap<java.lang.Long,java.lang.Long>(other.issue_stat);
      this.issue_stat = __this__issue_stat;
    }
  }

  public TMagicBeanIssueCountRet deepCopy() {
    return new TMagicBeanIssueCountRet(this);
  }

  @Override
  public void clear() {
    this.code = null;
    this.message = null;
    this.issue_stat = null;
  }

  /**
   * 
   * @see TErrorCode
   */
  public TErrorCode getCode() {
    return this.code;
  }

  /**
   * 
   * @see TErrorCode
   */
  public TMagicBeanIssueCountRet setCode(TErrorCode code) {
    this.code = code;
    return this;
  }

  public void unsetCode() {
    this.code = null;
  }

  /** Returns true if field code is set (has been assigned a value) and false otherwise */
  public boolean isSetCode() {
    return this.code != null;
  }

  public void setCodeIsSet(boolean value) {
    if (!value) {
      this.code = null;
    }
  }

  public java.lang.String getMessage() {
    return this.message;
  }

  public TMagicBeanIssueCountRet setMessage(java.lang.String message) {
    this.message = message;
    return this;
  }

  public void unsetMessage() {
    this.message = null;
  }

  /** Returns true if field message is set (has been assigned a value) and false otherwise */
  public boolean isSetMessage() {
    return this.message != null;
  }

  public void setMessageIsSet(boolean value) {
    if (!value) {
      this.message = null;
    }
  }

  public int getIssue_statSize() {
    return (this.issue_stat == null) ? 0 : this.issue_stat.size();
  }

  public void putToIssue_stat(long key, long val) {
    if (this.issue_stat == null) {
      this.issue_stat = new java.util.HashMap<java.lang.Long,java.lang.Long>();
    }
    this.issue_stat.put(key, val);
  }

  public java.util.Map<java.lang.Long,java.lang.Long> getIssue_stat() {
    return this.issue_stat;
  }

  public TMagicBeanIssueCountRet setIssue_stat(java.util.Map<java.lang.Long,java.lang.Long> issue_stat) {
    this.issue_stat = issue_stat;
    return this;
  }

  public void unsetIssue_stat() {
    this.issue_stat = null;
  }

  /** Returns true if field issue_stat is set (has been assigned a value) and false otherwise */
  public boolean isSetIssue_stat() {
    return this.issue_stat != null;
  }

  public void setIssue_statIsSet(boolean value) {
    if (!value) {
      this.issue_stat = null;
    }
  }

  public void setFieldValue(_Fields field, java.lang.Object value) {
    switch (field) {
    case CODE:
      if (value == null) {
        unsetCode();
      } else {
        setCode((TErrorCode)value);
      }
      break;

    case MESSAGE:
      if (value == null) {
        unsetMessage();
      } else {
        setMessage((java.lang.String)value);
      }
      break;

    case ISSUE_STAT:
      if (value == null) {
        unsetIssue_stat();
      } else {
        setIssue_stat((java.util.Map<java.lang.Long,java.lang.Long>)value);
      }
      break;

    }
  }

  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case CODE:
      return getCode();

    case MESSAGE:
      return getMessage();

    case ISSUE_STAT:
      return getIssue_stat();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case CODE:
      return isSetCode();
    case MESSAGE:
      return isSetMessage();
    case ISSUE_STAT:
      return isSetIssue_stat();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that == null)
      return false;
    if (that instanceof TMagicBeanIssueCountRet)
      return this.equals((TMagicBeanIssueCountRet)that);
    return false;
  }

  public boolean equals(TMagicBeanIssueCountRet that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_code = true && this.isSetCode();
    boolean that_present_code = true && that.isSetCode();
    if (this_present_code || that_present_code) {
      if (!(this_present_code && that_present_code))
        return false;
      if (!this.code.equals(that.code))
        return false;
    }

    boolean this_present_message = true && this.isSetMessage();
    boolean that_present_message = true && that.isSetMessage();
    if (this_present_message || that_present_message) {
      if (!(this_present_message && that_present_message))
        return false;
      if (!this.message.equals(that.message))
        return false;
    }

    boolean this_present_issue_stat = true && this.isSetIssue_stat();
    boolean that_present_issue_stat = true && that.isSetIssue_stat();
    if (this_present_issue_stat || that_present_issue_stat) {
      if (!(this_present_issue_stat && that_present_issue_stat))
        return false;
      if (!this.issue_stat.equals(that.issue_stat))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetCode()) ? 131071 : 524287);
    if (isSetCode())
      hashCode = hashCode * 8191 + code.getValue();

    hashCode = hashCode * 8191 + ((isSetMessage()) ? 131071 : 524287);
    if (isSetMessage())
      hashCode = hashCode * 8191 + message.hashCode();

    hashCode = hashCode * 8191 + ((isSetIssue_stat()) ? 131071 : 524287);
    if (isSetIssue_stat())
      hashCode = hashCode * 8191 + issue_stat.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(TMagicBeanIssueCountRet other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.valueOf(isSetCode()).compareTo(other.isSetCode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.code, other.code);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetMessage()).compareTo(other.isSetMessage());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMessage()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.message, other.message);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.valueOf(isSetIssue_stat()).compareTo(other.isSetIssue_stat());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIssue_stat()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.issue_stat, other.issue_stat);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("TMagicBeanIssueCountRet(");
    boolean first = true;

    sb.append("code:");
    if (this.code == null) {
      sb.append("null");
    } else {
      sb.append(this.code);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("message:");
    if (this.message == null) {
      sb.append("null");
    } else {
      sb.append(this.message);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("issue_stat:");
    if (this.issue_stat == null) {
      sb.append("null");
    } else {
      sb.append(this.issue_stat);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class TMagicBeanIssueCountRetStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public TMagicBeanIssueCountRetStandardScheme getScheme() {
      return new TMagicBeanIssueCountRetStandardScheme();
    }
  }

  private static class TMagicBeanIssueCountRetStandardScheme extends org.apache.thrift.scheme.StandardScheme<TMagicBeanIssueCountRet> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, TMagicBeanIssueCountRet struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // CODE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.code = com.yy.hd.api.thrift.fts.currency.TErrorCode.findByValue(iprot.readI32());
              struct.setCodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // MESSAGE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.message = iprot.readString();
              struct.setMessageIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // ISSUE_STAT
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map8 = iprot.readMapBegin();
                struct.issue_stat = new java.util.HashMap<java.lang.Long,java.lang.Long>(2*_map8.size);
                long _key9;
                long _val10;
                for (int _i11 = 0; _i11 < _map8.size; ++_i11)
                {
                  _key9 = iprot.readI64();
                  _val10 = iprot.readI64();
                  struct.issue_stat.put(_key9, _val10);
                }
                iprot.readMapEnd();
              }
              struct.setIssue_statIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, TMagicBeanIssueCountRet struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.code != null) {
        oprot.writeFieldBegin(CODE_FIELD_DESC);
        oprot.writeI32(struct.code.getValue());
        oprot.writeFieldEnd();
      }
      if (struct.message != null) {
        oprot.writeFieldBegin(MESSAGE_FIELD_DESC);
        oprot.writeString(struct.message);
        oprot.writeFieldEnd();
      }
      if (struct.issue_stat != null) {
        oprot.writeFieldBegin(ISSUE_STAT_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I64, org.apache.thrift.protocol.TType.I64, struct.issue_stat.size()));
          for (java.util.Map.Entry<java.lang.Long, java.lang.Long> _iter12 : struct.issue_stat.entrySet())
          {
            oprot.writeI64(_iter12.getKey());
            oprot.writeI64(_iter12.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class TMagicBeanIssueCountRetTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public TMagicBeanIssueCountRetTupleScheme getScheme() {
      return new TMagicBeanIssueCountRetTupleScheme();
    }
  }

  private static class TMagicBeanIssueCountRetTupleScheme extends org.apache.thrift.scheme.TupleScheme<TMagicBeanIssueCountRet> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, TMagicBeanIssueCountRet struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetCode()) {
        optionals.set(0);
      }
      if (struct.isSetMessage()) {
        optionals.set(1);
      }
      if (struct.isSetIssue_stat()) {
        optionals.set(2);
      }
      oprot.writeBitSet(optionals, 3);
      if (struct.isSetCode()) {
        oprot.writeI32(struct.code.getValue());
      }
      if (struct.isSetMessage()) {
        oprot.writeString(struct.message);
      }
      if (struct.isSetIssue_stat()) {
        {
          oprot.writeI32(struct.issue_stat.size());
          for (java.util.Map.Entry<java.lang.Long, java.lang.Long> _iter13 : struct.issue_stat.entrySet())
          {
            oprot.writeI64(_iter13.getKey());
            oprot.writeI64(_iter13.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, TMagicBeanIssueCountRet struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(3);
      if (incoming.get(0)) {
        struct.code = com.yy.hd.api.thrift.fts.currency.TErrorCode.findByValue(iprot.readI32());
        struct.setCodeIsSet(true);
      }
      if (incoming.get(1)) {
        struct.message = iprot.readString();
        struct.setMessageIsSet(true);
      }
      if (incoming.get(2)) {
        {
          org.apache.thrift.protocol.TMap _map14 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I64, org.apache.thrift.protocol.TType.I64, iprot.readI32());
          struct.issue_stat = new java.util.HashMap<java.lang.Long,java.lang.Long>(2*_map14.size);
          long _key15;
          long _val16;
          for (int _i17 = 0; _i17 < _map14.size; ++_i17)
          {
            _key15 = iprot.readI64();
            _val16 = iprot.readI64();
            struct.issue_stat.put(_key15, _val16);
          }
        }
        struct.setIssue_statIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}


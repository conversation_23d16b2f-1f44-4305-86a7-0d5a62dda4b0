package com.yy.hd.client;

import com.yy.hd.api.thrift.fts.currency.TFtsCurrencyService;
import lombok.experimental.Delegate;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @since 2025/6/27 16:41
 */
@Slf4j
@Component
public class FtsCurrencyClient extends BaseClient {

    @Delegate
    @Reference(protocol = "attach_nythrift", owner = "${s2sname.fts.currency}")
    public TFtsCurrencyService.Iface proxy;
}

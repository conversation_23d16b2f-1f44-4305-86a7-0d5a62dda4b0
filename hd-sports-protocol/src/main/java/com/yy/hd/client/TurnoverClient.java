package com.yy.hd.client;

import com.yy.hd.api.thrift.turnover.im.TImThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @since 2025/6/26 15:48
 */
@Slf4j
@Component
public class TurnoverClient extends BaseClient{

    @Reference(protocol = "attach_nythrift_compact", owner = "${s2sname.turnover.im}")
    public TImThriftService.Iface imService;
}

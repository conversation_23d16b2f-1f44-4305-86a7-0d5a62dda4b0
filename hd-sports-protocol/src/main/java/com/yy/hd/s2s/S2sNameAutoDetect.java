package com.yy.hd.s2s;

/**
 *
 * 自动根据环境识别 s2sname, 这样子就不用去apollo上配置了，代码里面一目了然
 * <AUTHOR>
 * @since 2025/6/26 17:39
 */
public class S2sNameAutoDetect {

    /**
     * 配置名 生产s2sname 测试s2sname
     * 后续正式和测试不一致的都可以定义到这里
     */
    private static final String[][] S2SNAME_ARRAY = new String[][]{
            new String[]{"s2sname.turnover.im", "to_im_service", "to_im_service_pre"},
            new String[]{"broadcast.gateway.s2s", "broadcast_gateway", "broadcast_gateway_test"},
            new String[]{"s2sname.fts.currency", "fts_currency", "fts_currency_test"},
            new String[]{"fts.userinfo.s2s", "fts_userinfo", "fts_userinfo_test"},
            new String[]{"fts.lbs.s2s", "fts_lbs", "fts_lbs_test"},
            new String[]{"fts.privilege.s2s", "fts_privilege", "fts_privilege_test"},
    };

    private static boolean inited = false;

    static {
        init();
    }

    public static void init() {
        if (inited) {
            return;
        }
        inited = true;
        String name = "MY_ENV_NAME";
        String env = System.getProperty(name, System.getenv(name));
        boolean prod = "prod".equals(env);
        for (String[] items : S2SNAME_ARRAY) {
            System.setProperty(items[0], prod ? items[1] : items[2]);
        }
    }
}
